<!-- 每小时产量柱状图，多系列图表 -->
<template>
    <div class="dev-status-pie">
        <v-chart class="chart" :option="chartOption" autoresize />
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import VChart from 'vue-echarts'
import * as echarts from "echarts"

import { getDevsProductionApi } from '@/api/aggregate'
import { useProductsStore } from '@/stores/modules/products'
import { ElMessage } from 'element-plus'
const pdStore = useProductsStore()
const props = defineProps({
    dev_ids: {
        type: Array,
        default: () => []
    },

    //时间范围，[startTime,endTime]
    time_range: {
        type: Array,
        default: () => []
    },
    //时间单位
    unit: {
        type: String,
        default: 'hour'
    },
    //图表名称
    chart_name: {
        type: String,
        default: ''
    }
})

const chartData = ref([])
const chartProps = ref([])
const chartTimeRange = ref([])

const loading = ref(false)

// 图表配置
const chartOption = computed(() => ({
    title: {// 图表标题
        text: props.chart_name
    },
    tooltip: {// 提示框
        show: true,
        trigger: 'axis',

    },
    toolbox: {
        feature: {
            magicType: {  // 动态类型切换
                type: ['line', 'bar', 'stack']
            }
        },
        right: '5%'
    },
    legend: {//曲线的图示
        data: chartProps.value?.map(prop => prop.name),
        top: '8%'
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',  // 增加底部空间，为数据区域滚动条留出位置
        containLabel: true
    },
    xAxis: {// X轴
        type: 'time',
    },
    yAxis: {// Y轴
        type: 'value',
    },
    dataset: {// 数据集
        dimensions: ['create_time', ...chartProps.value?.map(prop => prop.identifier)],
        source: chartData.value || []
    },
    series: chartProps.value?.map(prop => ({// 数据系列,如果是total改为line
        type: 'bar',
        name: prop.name,
        stack: 'total',
        barGap: '20%',
        barMaxWidth: 15,
        encode: {
            x: 'create_time',
            y: prop.identifier
        }
    }))
}))


const fetchProduction = async () => {
    const [startTime, endTime] = props.time_range
    if (!startTime || !endTime) {
        ElMessage.error('请选择时间范围')
        return
    }
    loading.value = true
    try {
        const params = {
            dev_ids: props.dev_ids,
            start_time: startTime,
            end_time: endTime,
            time_type: props.unit
        }
        let res = []
        res = await getDevsProductionApi(params)
        console.log('每小时产量', res)

        chartData.value = groupByCreateTime(res) || [];
    } catch (error) {
        ElMessage.error('获取每小时产量失败')
        console.error('获取每小时产量失败:', error)
    } finally {
        loading.value = false
    }
}


const groupByCreateTime = (data) => {
    const resultMap = {};
    // 遍历原始数据,data是一个对象数组,转为一个按时间合并的对象数组
    data.map(item => {
        // 把create_time转换为YYYY-MM-DD
        const { create_time, dev_id, production } = item;
        // 如果当前时间点不存在，初始化一个对象
        if (!resultMap[create_time]) {
            resultMap[create_time] = { create_time };
        }
        // 将 dev_id 作为键，production 作为值，添加到对应时间点的对象中
        resultMap[create_time][dev_id] = production;
    })

    // 将结果转换为数组    
    console.log('resultMap', Object.values(resultMap))
    return Object.values(resultMap)
}

//转格式化时间,YYYY-MM-DD HH:mm:ss
const formatDate = (date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    const second = date.getSeconds().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}
const getChartProps =async () => {
    // 获取设备名称
    let chartp=[]
    props.dev_ids.forEach(async dev_id => {
        let dev_name = (await pdStore.getDevInfo(dev_id)).name
        chartp.push({ name: dev_name, identifier: dev_id })
    })
    return chartp
}
//获取设备的名称
watch(() => props.dev_ids, async (newVal) => {
    if (newVal.length === 0) {
        console.log('没有选择设备')
        return
    }
    chartProps.value = await getChartProps()
}, { deep: true, immediate: true })

watch(() => props.time_range, async (newVal) => {
    
    await fetchProduction()
},{ deep: true })
onMounted(async () => {
    await fetchProduction()
})
</script>

<style scoped>
.dev-status-pie {
    width: 100%;
    height: 100%;
    min-height: 200px;
}

.chart {
    width: 100%;
    height: 100%;
    min-height: 200px;
}
</style>