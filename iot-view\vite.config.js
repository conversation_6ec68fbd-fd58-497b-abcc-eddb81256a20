import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  // css: {
  //   preprocessorOptions: {
  //     scss: {
  //       javascriptEnabled: true,
  //       additionalData: `@import "@/styles/var.scss";`
  //     }
  //   }
  // },
  server: {
    //代理服务
    open: true,
    compress: false,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8000/', // 后端接口的域名        
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, "/"),
      },
      '/sse': {
        target: 'http://127.0.0.1:8000/', // 后端接口的域名        
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/sse/, "/"),         
        configure: (proxy, options) => {
          // 确保不缓存响应
          proxy.on('proxyRes', function(proxyRes, req, res) {
            proxyRes.headers['content-type'] = 'text/event-stream'
            proxyRes.headers['Cache-Control'] = 'no-cache';            
            proxyRes.headers['connection'] = 'keep-alive'            
          });
        }

      },
      '/ws': {
        target: 'ws://127.0.0.1:8000/',
        changeOrigin: true,
        ws: true,
        rewrite: (path) => path.replace(/^\/ws/, "/"),
        timeout: 30000,
        secure: false,
        onError: (err, req, res) => {
          console.error('proxy error', err);
        }
      }
    }
  }
})
