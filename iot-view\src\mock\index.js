import Mock from 'mockjs'  //导入mockjs
import socket from '@/utils/socket'  // 添加 socket 导入

//使用Mock下面提供的mock方法进行需要模拟数据的封装
//参数1，是需要拦截的完整请求地址，参数2，是请求方式，参数3，是请求的模拟数据
Mock.setup({
    timeout: '300-400'  // 设置相应时间
})
Mock.XHR.prototype.withCredentials = true

//返回的是响应数据，即data内容
Mock.mock('/api/users/gettoken/', 'get', { csrftoken: 'mock-token' })

Mock.mock('/api/users/login/', 'post', (data) => {
    console.log('登录请求参数', JSON.parse(data.body))
    var user = JSON.parse(data.body).username

    var token = 'mock-token'
    if (user == 'sailstar') {
        return { username: 'sailstar' }
    } else if (user == 'test') {
        return { username: 'test' }
    } else {

    }
})

Mock.mock('/api/users/info/', 'get', function (options) {
    console.log('getUserInfoApi数据', options)

    return { //返回的是响应数据
        username: 'Admin',
        layout: { bigscreen: '大屏布局A' },
        roles: ['admin', 'user'] //模拟的请
    }
})
var productsArray = [
    { pro_id: "1001", name: "CEW-100水洗", icon: "washer.svg", group: '水洗' },
    { pro_id: "1004", name: "CEW-50水洗", icon: "washer.svg", group: '水洗' },
    { pro_id: "1002", name: "CED-100烘干", icon: "dryer.svg", group: '烘干' },
    { pro_id: "1003", name: "CED-50烘干", icon: "dryer.svg", group: '烘干' },
]

Mock.mock(/\/api\/products\/getProducts/, 'get', function (options) {
    // 获取 GET 请求的参数
    const params = new URLSearchParams(options.url.split('?')[1]);
    const pro_id = params.get('pro_id');
    console.log('getProducts参数', pro_id)
    if (pro_id) {
        var pro = productsArray.find(item => item.pro_id === pro_id);
        console.log('getProducts返回', pro)
        return pro;
    } else {
        return productsArray;
    }
})

var devicesArray = [
    { dev_id: "1201", name: "水洗机1", type: '类型A', status: '正常', alarm: '无报警', icon: "washer.svg", group: '水洗' },
    { dev_id: "1202", name: "水洗机2", type: '类型A', status: '正常', alarm: '无报警', icon: "washer.svg", group: '水洗' },
    { dev_id: "1203", name: "水洗机3", type: '类型A', status: '正常', alarm: '无报警', icon: "washer.svg", group: '水洗' },
    { dev_id: "1204", name: "水洗机4", type: '类型F', status: '正常', alarm: '无报警', icon: "washer.svg", group: '水洗' },
    { dev_id: "1205", name: "水洗机5", type: '类型F', status: '正常', alarm: '无报警', icon: "washer.svg", group: '水洗' },
    { dev_id: "1206", name: "水洗机6", type: '类型F', status: '正常', alarm: '无报警', icon: "washer.svg", group: '水洗' },

    { dev_id: "1301", name: "烘干机1", type: '类型B', status: '正常', alarm: '无报警', icon: "dryer.svg", group: '烘干' },
    { dev_id: "1302", name: "烘干机2", type: '类型B', status: '正常', alarm: '无报警', icon: "dryer.svg", group: '烘干' },
    { dev_id: "1303", name: "烘干机3", type: '类型C', status: '正常', alarm: '无报警', icon: "dryer.svg", group: '烘干' },
    { dev_id: "1304", name: "烘干机4", type: '类型C', status: '正常', alarm: '无报警', icon: "dryer.svg", group: '烘干' },

    { dev_id: "4001", name: "水量表", type: '类型E', status: '正常', alarm: '无报警', },
    { dev_id: "4101", name: "电量表", type: '类型E', status: '正常', alarm: '无报警', },
    { dev_id: "4201", name: "蒸汽流量表", type: '类型E', status: '正常', alarm: '无报警', },
]

Mock.mock(/\/api\/products\/getDevices/, 'get', function (options) {
    // 获取 GET 请求的参数
    const params = new URLSearchParams(options.url.split('?')[1]);
    const dev_id = params.get('dev_id');
    console.log('getDevices参数', dev_id)
    if (dev_id) {
        var dev = devicesArray.find(item => item.dev_id === dev_id);
        console.log('getDevices返回', dev)
        return dev;
    } else {
        return devicesArray;
    }
})

var node = { create_time: '时间', wttemp: '水温', ph: 'pH值', level: '水位', speed: '速度', outemp: '出口温度', intemp: '进口温度', real: '实时值' }

var nodeData = [{
    'dev_id': "1201", nodes: [
        { name: '水温1', code: 'temp', type: 'number' },
        { name: 'PH值', code: 'ph', type: 'number' },
        { name: '水位', code: 'level', type: 'number' },
    ]
},
{
    'dev_id': "1202", nodes: [
        { name: '水温2', code: 'temp', type: 'number' },
        { name: 'PH值2', code: 'ph', type: 'number' },
        { name: '水位', code: 'level', type: 'number' },
    ]
},

]
//Mock.mock('/api/products/getDevicesAttr/', 'get', nodeData)

Mock.mock(/\/api\/products\/getDeviceAttr/, 'get', function (options) {
    // 获取 GET 请求的参数    
    const params = new URLSearchParams(options.url.split('?')[1]);
    const dev_id = params.get('dev_id');

    let nd = nodeData.find(item => item.dev_id === dev_id);
    if (nd) {
        return nd;
    } else {
        return nodeData[0]
    }

    return nodeData.find(item => item.dev_id === dev_id);
})
var productProps = [{
    'pro_id': "1001", nodes: [
        { name: '水温1', code: 'temp', dataType: 'number' },
        { name: 'PH值', code: 'ph', dataType: 'number' },
        { name: '水位', code: 'level', dataType: 'number' },
    ]
},
{
    'pro_id': "1002", nodes: [
        { name: '水温2', code: 'temp', dataType: 'number' },
        { name: 'PH值2', code: 'ph', dataType: 'number' },
        { name: '水位', code: 'level', dataType: 'number' },
    ]
},
]

Mock.mock(/\/api\/products\/getProductProps/, 'get', function (options) {
    // 获取 GET 请求的参数    
    const params = new URLSearchParams(options.url.split('?')[1]);
    const pro_id = params.get('pro_id');
    console.log('getProductProps参数', pro_id)
    let pp = productProps.find(item => item.pro_id === pro_id);
    if (pp) {
        return pp;
    } else {
        return productProps[0];
    }

})


var eventData = [
    {
        dev_id: "1201",
        events: [
            {
                createTime: "2023-08-01 12:00:00",
                type: "报警",
                name: "设备温度过高1"
            },
            {
                createTime: "2023-08-02 14:30:00",
                type: "故障",
                name: "设备电源故障1"
            }]
    },
    {
        dev_id: "1202",
        events: [
            {
                createTime: "2023-08-01 12:00:00",
                type: "报警",
                name: "设备温度过高2"
            },
            {
                createTime: "2023-08-02 14:30:00",
                type: "故障",
                name: "设备电源故障2"
            }]
    }
]

Mock.mock(/\/api\/products\/getDeviceEvent/, 'get', function (options) {
    // 获取 GET 请求的参数    
    const params = new URLSearchParams(options.url.split('?')[1]);
    const dev_id = params.get('dev_id');

    let nd = eventData.find(item => item.dev_id === dev_id);
    if (nd) {
        return nd;
    } else {
        return eventData[0]
    }
})

// 添加模拟 socket 数据推送
function mockSocketData() {
    // 模拟设备列表
    const devices = ['1201', '1202', '1203', '1204', '1301', '1302']

    // 模拟状态数据
    function mockState() {
        const dev_id = devices[Math.floor(Math.random() * devices.length)]
        const states = ['running', 'stopped', 'offline']
        return {
            type: 'state',
            dev_id,
            status: states[Math.floor(Math.random() * states.length)]
        }
    }

    // 模拟属性数据
    function mockProps() {
        const dev_id = devices[Math.floor(Math.random() * devices.length)]
        return {
            type: 'props',
            dev_id,
            props: {
                temp: Mock.Random.float(20, 80, 1, 1),
                ph: Mock.Random.float(6, 8, 1, 1),
                level: Mock.Random.float(0, 100, 0, 1),
                speed: Mock.Random.float(0, 1500, 0, 0)
            }
        }
    }

    // 模拟事件数据
    function mockEvent() {
        const dev_id = devices[Math.floor(Math.random() * devices.length)]
        const eventTypes = ['警告', '故障', '信息']
        const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)]

        const events = {
            '警告': ['温度过高', '水位过低', 'PH值异常'],
            '故障': ['通信故障', '传感器故障', '电机故障'],
            '信息': ['启动运行', '停止运行', '参数已更新']
        }

        return {
            type: 'event',
            dev_id,
            event: {
                type: eventType,
                name: events[eventType][Math.floor(Math.random() * events[eventType].length)],
                timestamp: new Date().getTime()
            }
        }
    }

    // 定时发送模拟数据
    setInterval(() => {
        if (!socket.websocket || !socket.socket_open) return  // 添加连接检查

        // 模拟状态更新 (每10秒)
        if (Math.random() < 0.3) { // 30%概率发送状态
            const data = mockState()
            socket.websocket.send(JSON.stringify(data))
        }

        // 模拟属性更新 (频繁)
        const propsData = mockProps()
        socket.websocket.send(JSON.stringify(propsData))

        // 模拟事件 (偶尔)
        if (Math.random() < 0.1) { // 10%概率发送事件
            const eventData = mockEvent()
            socket.websocket.send(JSON.stringify(eventData))
        }
        console.log('mockSocketData')
    }, 2000)
}

