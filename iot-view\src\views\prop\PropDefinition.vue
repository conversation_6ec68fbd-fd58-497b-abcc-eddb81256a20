<template>
    <div>
        <!-- 属性定义列表 -->
        <div class="section">
            <div class="section-header">                
                <el-button type="primary" @click="addProperty">添加属性</el-button>
            </div>

            <el-table :data="properties" border>
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column label="属性名称" prop="name"></el-table-column>
                <el-table-column label="属性标识符" prop="identifier"></el-table-column>
                <el-table-column label="属性类型" prop="type" width="100">
                    <template #default="{ row }">
                        <el-tag :type="getPropTypeTag(row.type)">
                            {{ getPropTypeLabel(row.type) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="数据类型" prop="dataType">
                    <template #default="{ row }">
                        <span>Number (数值)</span>
                        <span class="unit">单位: {{ row.unit }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                    <template #default="{ row }">
                        <el-button-group>
                            <el-button size="small" :icon="Edit" @click="editProperty(row)"></el-button>
                            <el-button size="small" :icon="Delete" type="danger"
                                @click="deleteProperty(row)"></el-button>
                        </el-button-group>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-dialog v-model="editPropDialog"  width="50%">
            <!-- 属性表单组件 -->
            <PropEdit :pro_id="props.pro_id" :propData="currentProp" @close="handleClose" />
        </el-dialog>
        
    </div>

</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useProductsStore } from '@/stores/modules/products';

import PropEdit from './PropEdit.vue'

const pdStore = useProductsStore()
// props 定义
const props = defineProps({
    pro_id: {
        type: String,
        required: true
    }
})

// 响应式数据
const properties = ref([])

const editPropDialog = ref(false)

// 获取产品属性数据
const fetchProductProps = async () => {    
    try {
        const nodes = await pdStore.requestProductProps(props.pro_id)
        properties.value = nodes || []
        //events.value = res.events || []
    } catch (error) {
        ElMessage.error('获取产品属性失败')
    }
}

// 添加属性
const addProperty = () => {
    editPropDialog.value = true
}
const currentProp = ref(null)
// 编辑属性
const editProperty = (row) => {
    editPropDialog.value = true
    currentProp.value = row
}

// 关闭属性编辑弹窗
const handleClose = () => {     
    editPropDialog.value = false
    fetchProductProps()
}

// 删除属性
const deleteProperty = async (row) => {
    try {
        await ElMessageBox.confirm('确认删除该属性?', '提示', {
            type: 'warning'
        })

        await pdStore.deleteProductProp({
            pro_id: props.pro_id,
            prop_id: row.id
        })
        ElMessage.success('删除成功')
        await fetchProductProps()
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('删除失败')
        }
    }
}


// 获取属性类型标签
const getPropTypeTag = (type) => {
    const map = {
        'PROP': 'success',
        'EVENT': 'info',
        'CMD': 'danger'
    }
    return map[type] || 'info'
}

//获取属性类型名称
const getPropTypeLabel = (type) => {
    const map = {
        'PROP': '属性',
        'EVENT': '事件',
        'CMD': '指令'
    }
    return map[type] || type
}

// 生命周期钩子
onMounted(() => {
    fetchProductProps()
})
</script>

<style scoped>
.section {
    margin-bottom: 20px;
    height: 100%;
    overflow-y: auto;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.unit {
    margin-left: 10px;
    color: #999;
}
</style>
