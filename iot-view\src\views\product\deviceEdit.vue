<template>
    <!-- 设备新建或编辑表单 -->
    <div class="property-form">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" size="default">
            <el-form-item label="设备名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入产品名称"></el-input>
            </el-form-item>
            <el-form-item label="所属产品" prop="pro_id">
                <el-select v-model="formData.pro_id" placeholder="选择产品" :disabled="!(props.pro_id === undefined)">
                    <el-option v-for="pd in allProducts" :key="pd.pro_id" :label="pd.name" :value="pd.pro_id" />
                </el-select>

            </el-form-item>
            <el-form-item label="连接类型" prop="connect_type">
                <el-select v-model="formData.connect_type" placeholder="选择连接类型">
                    <el-option label="网关子设备" value="SUBDEV"></el-option>
                    <el-option label="直连设备" value="DIRECT"></el-option>
                    <el-option label="网关" value="GATEWAY"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="产量计算方式" prop="production">
                <el-select v-model="formData.production" placeholder="请选择">
                    <el-option label="无产出" value="NONE"></el-option>
                    <el-option label="按程序产出" value="PROG"></el-option>
                    <el-option label="连续产出" value="TOTAL"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="备注" prop="remark"></el-form-item>
        </el-form>
        <div class="footer">
            <el-button type="primary" @click="submitForm">{{ isEdit ? '更新' : '新建' }}</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { updateDevApi, getDevDetailApi, getProductsApi } from '@/api/products'

const props = defineProps({
    pro_id: {
        type: String
    },
    dev_id: {
        type: String
    }
})

const emit = defineEmits(['success'])

const formRef = ref(null)
const isEdit = ref(false)

// 表单数据
const formData = ref({
    name: '',
    pro_id: '',
    production: 'NONE',
    connect_type: 'DIRECT',
    remark: '',
})

// 表单验证规则
const rules = {
    name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
    pro_id: [{ required: true, message: '请选择产品', trigger: 'change' }],
}

watch(() => props.dev_id, async (newVal) => {
    // 重置表单
    formData.value = {
        name: '',
        pro_id: '',
        production: 'NOT',
        connect_type: 'SUBDEV',
        remark: '',
    }
    isEdit.value = false
    if (newVal) {
        isEdit.value = true
        // 获取设备详情
        const res = await getDevDetailApi(newVal)
        formData.value = { ...res }
    }
}, { immediate: true })

const allProducts = ref([])
watch(() => props.pro_id, async (newVal) => {
    formData.value.pro_id = props.pro_id
    allProducts.value = await getProductsApi(newVal)
}, { immediate: true })


// 提交表单
const submitForm = async () => {
    if (!formRef.value) return

    try {
        await formRef.value.validate()
        //只保留需要的几个
        const devInfo = {
            name: formData.value.name,
            pro_id: formData.value.pro_id,
            production: formData.value.production,
            connect_type: formData.value.connect_type,
            remark: formData.value.remark
        }
        if (props.pro_id) {
            devInfo.pro_id = props.pro_id
        }
        if (props.dev_id) {
            devInfo.id = props.dev_id
        }
        console.log('device', devInfo)
        const res = await updateDevApi(devInfo)
        if (res.status === 'error') {
            ElMessage.error('操作失败: ' + res.message)
            return
        }
        ElMessage.success('更新成功')
        // 通知父组件刷新列表        
        formRef.value.resetFields()
        emit('success')
    } catch (error) {
        ElMessage.error(isEdit.value ? '更新失败' : '添加失败')
        console.error('操作失败:', error)
    }
}


</script>
<style scoped>
.property-form {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
}

.header {
    margin-bottom: 20px;
}

.el-form-item {
    .el-input {
        width: 300px;
        height: 40px;
    }

    .el-select {
        width: 300px;
        height: 40px;
    }
}

.rule-form {
    max-width: 600px;
}
</style>