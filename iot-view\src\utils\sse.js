import { ref } from 'vue'

class SSEClient {
    constructor() {
        this.eventSource = null
        this.url = '/sse/sse/data/'
        this.isConnected = ref(false)
        this.messageCallback = null
        this.reconnectAttempts = 0
        this.maxReconnectAttempts = 5
        this.reconnectInterval = 5000
        this.isManualClosed = false

        // --- 新增：心跳监控相关属性 ---
        this.heartbeatMonitor = null      // 心跳监视器的定时器ID
        this.lastHeartbeatTimestamp = 0 // 上次收到心跳的时间戳
        // 超时时间，应略大于后端心跳间隔。后端20秒一次，这里设置45秒。
        this.heartbeatTimeout = 45000     
    }

    connect(deviceIds = [], callback) {
        if (this.eventSource && this.isConnected.value) {
            console.log('SSE已连接，不需要重复连接')
            return
        }

        this.close() // 先确保彻底关闭旧的连接和监视器

        this.messageCallback = callback
        this.isManualClosed = false

        let url = this.url
        if (deviceIds.length > 0) {
            const params = deviceIds.map(id => `device_ids=${encodeURIComponent(id)}`).join('&')
            url += `?${params}`
        }

        console.log('尝试连接SSE:', url)

        try {
            this.eventSource = new EventSource(url, { withCredentials: true })

            this.eventSource.onopen = () => {
                console.log('SSE连接已建立')
                this.isConnected.value = true
                this.reconnectAttempts = 0
                // --- 修改：连接成功后，重置心跳时间并启动监视器 ---
                this.lastHeartbeatTimestamp = Date.now()
                this.startHeartbeatMonitor()
            }

            this.eventSource.onmessage = (event) => {
                // --- 修改：收到任何消息都代表连接是活跃的，更新心跳时间 ---
                this.lastHeartbeatTimestamp = Date.now()

                console.log('收到SSE原始消息:', event.data)
                try {
                    const data = JSON.parse(event.data)
                    // ... 您的消息处理逻辑保持不变 ...
                    switch(data.type) {
                        case 'connected':
                            console.log('连接成功，客户端ID:', data.client_id)
                            break
                        case 'device_data':
                            console.log('设备数据更新:', data.device_id, data.data)
                            if (this.messageCallback) {
                                this.messageCallback(data.data)
                            }
                            break
                        case 'heartbeat':
                            console.log('心跳:', data.timestamp) // 心跳消息本身也会更新上面的时间戳
                            break
                        default:
                            console.log('未知消息类型:', data.type)
                    }
                } catch (error) {
                    console.error('解析SSE消息失败:', error, event.data)
                }
            }

            this.eventSource.onerror = (event) => {
                console.error('SSE连接错误:', event)
                this.handleDisconnect() // 统一处理断开逻辑
            }

        } catch (error) {
            console.error('SSE连接失败:', error)
            this.handleDisconnect()
        }
    }

    // --- 新增：启动心跳监视器的方法 ---
    startHeartbeatMonitor() {
        // 先清除旧的，以防万一
        if (this.heartbeatMonitor) {
            clearInterval(this.heartbeatMonitor)
        }

        this.heartbeatMonitor = setInterval(() => {
            if (Date.now() - this.lastHeartbeatTimestamp > this.heartbeatTimeout) {
                console.warn(`在 ${this.heartbeatTimeout / 1000} 秒内未收到心跳，判定连接超时。`)
                this.handleDisconnect() // 超时，执行断开逻辑
            }
        }, 5000) // 每5秒检查一次
    }

    // --- 新增：统一的断开处理函数 ---
    handleDisconnect() {
        if (this.isManualClosed) return; // 如果是手动关闭的，则不执行重连

        console.log("执行断开处理逻辑...")
        this.closeInternals() // 关闭内部连接和定时器
        this.reconnect([], this.messageCallback) // 尝试重连
    }

    // 重连机制
    reconnect(deviceIds, callback) {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('达到最大重连次数，停止重连。')
            return
        }

        this.reconnectAttempts++
        console.log(`将尝试第 ${this.reconnectAttempts} 次重连...`)

        setTimeout(() => {
            this.connect(deviceIds, callback)
        }, this.reconnectInterval)
    }

    // --- 修改：内部关闭方法，只负责清理，不改变手动关闭标志 ---
    closeInternals() {
        this.isConnected.value = false
        if (this.eventSource) {
            this.eventSource.close()
            this.eventSource = null
        }
        if (this.heartbeatMonitor) {
            clearInterval(this.heartbeatMonitor)
            this.heartbeatMonitor = null
        }
    }

    // --- 修改：公开的 close 方法，供外部调用 ---
    close() {
        console.log('手动断开SSE连接')
        this.isManualClosed = true
        this.reconnectAttempts = this.maxReconnectAttempts + 1; // 阻止任何后续的自动重连
        this.closeInternals()
    }

    getConnectedStatus() {
        return this.isConnected.value
    }
}

const sseClient = new SSEClient()
export default sseClient