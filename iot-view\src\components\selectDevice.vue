<template>
    <div class="select-device">
        <el-select v-model="selectedDevices" multiple filterable clearable collapse-tags collapse-tags-tooltip
            placeholder="全部" class="device-select" @change="handleChange">
            <el-option v-for="device in deviceList" :key="device.dev_id" :label="device.name" :value="device.dev_id">
                <div class="device-option">
                    <span>{{ device.name }}</span>                    
                </div>
            </el-option>
        </el-select>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useProductsStore } from '@/stores/modules/products'
import { ElMessage } from 'element-plus'

const props = defineProps({
    // 占位符文本

})

const emit = defineEmits(['update:modelValue', 'change'])

const pdStore = useProductsStore()
const deviceList = ref([])
const selectedDevices = ref([])

// 获取设备列表
const fetchDeviceList = async () => {
    try {
        const devices = await pdStore.getDevs()
        deviceList.value = devices
    } catch (error) {
        console.error('获取设备列表失败:', error)
        ElMessage.error('获取设备列表失败')
    }
}



// 选择变更处理
const handleChange = (value) => {
    emit('update:modelValue', value)
    emit('change', value)
}


// 组件挂载时获取设备列表
onMounted(async () => {
    await fetchDeviceList()

    //默认全部选中
    emit('update:modelValue', deviceList.value.map(item => item.dev_id))
})

// 对外暴露刷新方法
defineExpose({
    refresh: fetchDeviceList
})
</script>

<style scoped>
.select-device {
    display: inline-block;
}

.device-select {
    width: 100%;
}

.device-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 4px;
}

:deep(.el-select-dropdown__item) {
    padding: 0 10px;
}
</style>
