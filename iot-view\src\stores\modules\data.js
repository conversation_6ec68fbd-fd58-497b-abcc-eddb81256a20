import { defineStore } from 'pinia'
import socket from '@/utils/socket'
import sseClient from '@/utils/sse'
import Mock from 'mockjs'
import { getDevsStatusApi,getDevsStatusCurrentApi} from '@/api/data'

//设备状态映射
const status_map = {
    'RUN':'运行',
    'STANDBY':'待机',
    'OFFLINE':'离线',
    'FAULT':'故障',
    'ALARM':'告警'
}

export const useDataStore = defineStore('data', {
    state: () => ({
        // 设备状态数据 { dev_id: [status] }
        devState: {},
        // 设备属性数据 { dev_id: [[{ prop_key: value }],] }
        devProps: {},
        // 设备事件数据 { dev_id: [[{ event_type, data }],] }
        devEvents: {},
        //工艺详情
        processDetail:{},
        // WebSocket连接状态
        isConnected: socket.isConnected,
        // 添加SSE相关状态
        sseConnection: null,
        isSSEConnected: false,
        
    }),

    getters: {
        // 获取指定设备的属性
        getDeviceProps: (state) => (devId) => {
            return state.devProps[devId] || []
        },
        // 获取指定设备的事件
        getDeviceEvents: (state) => (devId) => {
            return state.devEvents[devId] || []
        },
        // 获取指定设备的最新状态
        getDeviceState: async (state) => {
            return async (devId) => {
                let devState = state.devState[devId]
                if(!devState){
                    //请求最新状态
                    const res = await state.requestDevsState([devId])
                    if (res.length > 0) {
                        devState = {
                            status:res[0].status,
                            name:status_map[res[0].status],
                            create_time:res[0].end_time
                        }                    
                    }
                }
                return devState
            }
        },        
    },

    actions: {
        // 获取指定设备的最新状态
        async requestDevsState(devIds){            
            const res = await getDevsStatusCurrentApi(devIds)
            res.forEach(item=>{
                item.name=status_map[item.status]
                this.devState[item.dev_id] = item
            })
            return res
        },

        //获取洗涤工艺详情
        async requestProcessDetail(params){
            const res = await getDevsProcessDetailApi(params)
            res.forEach(item=>{
                let pr_id=item.process_id
                if (!this.processDetail.hasOwnProperty(pr_id)){
                    this.processDetail[pr_id] = []
                }
                this.processDetail[pr_id].push(item)
            })
            
            return res
        },

        // 处理接收到的消息
        handleMessage(data) {            
            console.log('收到WebSocket消息:', data)
            const { dev_id, timestamp, props, events, status } = data
            if (!dev_id||!timestamp) {
                console.error('dev_id or timestamp is missing')
                return
            }
            // 处理设备状态
            if (status) {
                this.devState[dev_id] = {
                    status:status,
                    name:status_map[status],
                    create_time:timestamp
                }
            }

            // 处理设备属性,判断是否空对象
            if (props&&Object.keys(props).length > 0) {
                if (!this.devProps[dev_id]) {
                    this.devProps[dev_id] = []
                }
                props.create_time = timestamp
                this.devProps[dev_id].push(props)
                // 限制属性历史记录数量,截取100条
                if (this.devProps[dev_id].length > 100) {
                    this.devProps[dev_id].shift()
                }
            }

            // 处理设备事件
            if (events) {
                if (!this.devEvents[dev_id]) {
                    this.devEvents[dev_id] = []
                }
                events.forEach(event => {
                    event.timestamp = timestamp
                    this.devEvents[dev_id].unshift(event)
                })
                // 限制事件历史记录数量,截取100条
                this.devEvents[dev_id] = this.devEvents[dev_id].slice(0, 100)
            }
        },

        // 生成模拟数据
        generateMockData() {
            // 模拟设备列表
            const devices = ['1201', '1202', '1203', '1204', '1205']

            // 随机选择一个设备
            const dev_id = devices[Math.floor(Math.random() * devices.length)]
            //const dev_id = '1201'

            // 生成状态数据
            if (Math.random() < 0.5) { // 30%概率发送状态
                const states = ['running', 'stopped', 'offline']
                this.handleMessage({
                    type: 'state',
                    dev_id: dev_id,
                    timestamp: new Date().getTime(),
                    status: states[Math.floor(Math.random() * states.length)]
                })
            }

            // 生成属性数据
            this.handleMessage({
                type: 'props',
                dev_id: dev_id,
                timestamp: new Date().getTime(),
                props: {
                    temp: Mock.Random.float(20, 80, 1, 1),
                    ph: Mock.Random.float(6, 8, 1, 1),
                    level: Mock.Random.float(0, 100, 0, 1),
                    speed: Mock.Random.float(0, 1500, 0, 0),
                    timestamp: new Date().getTime()
                }
            })

            // 生成事件数据
            if (Math.random() > 0.5) { // 10%概率发送事件
                const eventTypes = ['警告', '故障', '信息']
                const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)]

                const events = {
                    '警告': ['温度过高', '水位过低', 'PH值异常'],
                    '故障': ['通信故障', '传感器故障', '电机故障'],
                    '信息': ['启动运行', '停止运行', '参数已更新']
                }

                this.handleMessage({
                    dev_id: dev_id,
                    timestamp: new Date().getTime(),
                    events: {
                        type: eventType,
                        identifier: events[eventType][Math.floor(Math.random() * events[eventType].length)],
                        timestamp: new Date().getTime()
                    }
                })
            }
        },

        // 开始模拟数据推送
        startMockData() {
            this.stopMockData() // 先停止之前的定时器
            this.mockTimer = setInterval(() => {
                this.generateMockData()
                //console.log('Mock data generated')
            }, 3000)
        },

        // 停止模拟数据推送
        stopMockData() {
            if (this.mockTimer) {
                clearInterval(this.mockTimer)
                this.mockTimer = null
            }
        },

        // 建立WebSocket连接
        connectSocket() {
            console.log('连接WebSocket')
            if (socket.isConnected) {
                console.log('WebSocket已连接,不需要重复连接')                
                return
            }

            if (import.meta.env.DEV) {
                // 开发环境使用模拟数据
                //this.startMockData()
                socket.connect(this.handleMessage.bind(this))                
                this.isConnected = socket.isConnected
            } else {
                socket.connect(this.handleMessage.bind(this))                
                this.isConnected = socket.isConnected
            }
        },

        // 断开WebSocket连接
        disconnectSocket() {
            console.log('断开WebSocket连接')
            if (import.meta.env.DEV) {
                //this.stopMockData()
                socket.close()                
            } else if (socket.socket_open) {
                socket.close()                
            }
        },

        // 建立SSE连接
        connectSSE(deviceIds = []) {            
            // 连接并传入消息处理函数
            sseClient.connect(deviceIds, (data) => {
                // 处理接收到的消息                
                this.handleMessage(data)
            })            
        },

        // 断开SSE连接
        disconnectSSE() {
            
            // 关闭连接
            sseClient.close()
        }
        
    }
})
