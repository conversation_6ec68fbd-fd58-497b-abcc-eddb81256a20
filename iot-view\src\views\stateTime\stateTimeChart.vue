<template>
    <!-- 设备时间甘特图，时间效率 -->
    <div class="history-container">
        <!-- 标题栏 -->
        <el-page-header @back="goBack">
            <template #content>
                <span class="text-large font-600 mr-3">设备时间效率 </span>
            </template>
        </el-page-header>
        <el-divider style="margin: 20px 0px 10px 0px;" />
        <!-- 时间选择和操作按钮 -->
        <div class="toolbar">
            <el-date-picker v-model="timeValue" type="date" value-format="YYYY-MM-DD" @change="fetchHistoryData" />
            <div class="buttons">
                <el-button type="success" :icon="Download" @click="exportData">导出</el-button>
            </div>
        </div>
        <!-- 时间效率甘特图 -->
        <div class="dev-status-gantt">
            <div class="dev-status-gantt-header">
                <!-- 显示各设备的效率数字 -->
                <div class="dev-efficiency">
                    <div class="dev-efficiency-item" v-for="dev in props.dev_ids" :key="dev">
                        {{ devNameMap[dev] }}：{{ devTimeEfficiency[dev] }}%
                    </div>
                </div>
                <!-- 显示各状态对应颜色 -->
                <div class="status-colors">
                    <div class="status-color" v-for="status in Object.keys(statusColors)" :key="status">
                        <div class="status-color-name">
                            {{ statusNameMap[status] }}
                        </div>
                        <div class="status-color-bar" :style="{ backgroundColor: statusColors[status] }"></div>
                    </div>
                </div>

            </div>
            <v-chart class="chart" :option="chartOption" autoresize />
        </div>

        <!-- 数据表格 -->
        <el-table ref="tableRef" :data="tableData" style="height:calc(100vh - 200px)" border v-loading="loading">
            <!-- 按时间筛选、排序 -->
            <el-table-column label="设备" width="180" fixed="left" :sortable="true">
                <template #default="{ row }">
                    {{ devNameMap[row.dev_id] }}
                </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="180" fixed="left" :sortable="true">
                <template #default="{ row }">
                    {{ statusNameMap[row.status] }}
                </template>
            </el-table-column>
            <el-table-column prop="duration" label="持续时间" width="180" fixed="left" />
            <el-table-column prop="create_time" label="开始时间" width="180" fixed="left" :sortable="true" />
            <el-table-column prop="end_time" label="结束时间" width="180" fixed="left" />
        </el-table>
    </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, TrendCharts } from '@element-plus/icons-vue'
import { getDevsStatusHistoryApi } from '@/api/data'
import { useProductsStore } from '@/stores/modules/products'
import router from '@/router'
import VChart from 'vue-echarts'
import * as echarts from "echarts"


const props = defineProps({
    dev_ids: {
        type: Array,
        default: ["WSH25001", "WSH25002", "FOD25001", "FOD25002"],
    }
})
const pdStore = useProductsStore()
const tableRef = ref(null)

const timeValue = ref('')
const tableData = ref([])

//获取设备的名称
const devNameMap = ref({})
const loading = ref(false)

// 颜色配置
const statusColors = {
    'RUN': '#409EFF',
    'STANDBY': '#E6A23C',
    'FAULT': '#F56C6C',
    'OFFLINE': '#909399'
};

//状态名称
const statusNameMap = {
    'RUN': '运行',
    'STANDBY': '待机',
    'FAULT': '故障',
    'OFFLINE': '离线'
}


const fetchHistoryData = async () => {

    if (!timeValue.value) {
        ElMessage.warning('请选择时间')
        return
    }

    loading.value = true
    try {
        const startTime = timeValue.value + ' 00:00:00'
        const endTime = timeValue.value + ' 23:59:59'

        const params = {
            dev_ids: props.dev_ids,
            //时间转字符串格式化
            start_time: startTime,
            end_time: endTime,
        }

        const res = await getDevsStatusHistoryApi(params)
        console.log(res)
        //重置排序
        tableRef.value.clearSort()
        tableData.value = res || []
    } catch (error) {
        ElMessage.error('获取历史数据失败')
        console.error('获取历史数据失败:', error)
    } finally {
        loading.value = false
    }
}


const filterData = (value, row, column) => {
    // 筛选数据
    const property = column['property']
    return row[property] === value
}

// 格式化甘特图数据
const formatGanttData = (data) => {
    if (!data || data.length === 0) return [];
    // 创建映射表用于分组存储设备每日记录
    const deviceDayMap = new Map();

    // 遍历数据创建分组
    data.forEach(record => {
        const dateKey = record.create_time.slice(0, 10); // 提取YYYY-MM-DD
        const mapKey = `${record.dev_id}_${dateKey}`;

        if (!deviceDayMap.has(mapKey)) {
            deviceDayMap.set(mapKey, []);
        }
        deviceDayMap.get(mapKey).push(record);
    });

    // 需要删除的记录集合
    const recordsToDelete = new Set();

    // 处理每个分组
    deviceDayMap.forEach((records, key) => {
        // 按时间排序
        records.sort((a, b) =>
            new Date(a.create_time) - new Date(b.create_time));

        // 获取最后一条记录
        const lastRecord = records[records.length - 1];

        // 检查是否需要删除
        if (lastRecord.status === 'OFFLINE') {
            recordsToDelete.add(lastRecord);
        }
    });

    // 过滤原始数据
    return data.filter(record => !recordsToDelete.has(record)).map(item => ({
        dev_id: item.dev_id,
        status: item.status,
        start: new Date(item.create_time),
        end: new Date(item.end_time),
        duration: Math.round((new Date(item.end_time).getTime() - new Date(item.create_time).getTime()) / (60 * 1000))
    }));
};

// 图表配置
const chartOption = computed(() => ({
    title: {
        text: '设备状态时间统计'
    },
    // Dataset配置   
    dataset: {
        source: chartData.value
    },
    tooltip: {
        trigger: 'item',
        formatter: (params) => {
            const data = params.data;
            return ` 
                        状态：${statusNameMap[data.status]}<br/>  
                        时间：${data.duration}分钟 <br/>                     
                        起止时间: ${data.start.toLocaleString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false })} - 
                         ${data.end.toLocaleString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false })}<br/>   
                        
                    `;
        }
    },
    xAxis: {
        type: 'time',
    },
    yAxis: {
        type: 'category',
        data: props.dev_ids,
        axisLabel: {    //显示设备的名称而不是id
            formatter: (value) => {
                return devNameMap.value[value]
            }
        }
    },
    series: [
        {
            type: 'custom',
            renderItem: (params, api) => {
                const yIndex = api.value(0);
                const start = api.coord([api.value(2), yIndex]);
                const end = api.coord([api.value(3), yIndex]);
                const height = api.size([0, 1])[1] * 0.3;

                return {
                    type: 'rect',
                    shape: {
                        x: start[0],
                        y: start[1] - height / 2,
                        width: end[0] - start[0],
                        height: height
                    },
                    style: {
                        fill: statusColors[api.value(1)]
                    }
                };
            },
            encode: {
                y: 0,
                x: [2, 3],
                itemName: 0
            },
        }
    ],
    legend: {

    },
    dataZoom: [
        {
            type: 'slider',  // 滑动条型数据区域缩放组件
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
            bottom: '5%'  // 放置在底部
        },
        {
            type: 'inside',  // 内置型数据区域缩放组件
            xAxisIndex: [0],
            start: 0,
            end: 100
        }
    ],

}));


// 查看数据曲线
const historyChartDialog = ref(false)
const chartData = computed(() => formatGanttData(tableData.value))

//计算设备时间效率
const devTimeEfficiency = computed(() => {
    //先把各设备的状态时间累加起来
    const stateTimeTotal = chartData.value.reduce((acc, item) => {
        if (!(item.dev_id in acc)) {
            acc[item.dev_id] = { "RUN": 0, "STANDBY": 0, "FAULT": 0, "OFFLINE": 0 }
        }
        let duration = item.end.getTime() - item.start.getTime()
        acc[item.dev_id][item.status] += duration
        return acc
    }, {})

    //然后计算每个设备的时间效率
    const devEfficiency = {}
    Object.keys(stateTimeTotal).map(dev_id => {
        let total = stateTimeTotal[dev_id].RUN + stateTimeTotal[dev_id].STANDBY + stateTimeTotal[dev_id].FAULT + stateTimeTotal[dev_id].OFFLINE
        devEfficiency[dev_id] = (stateTimeTotal[dev_id].RUN / total).toFixed(2) * 100
    })
    console.log("时间效率", devEfficiency)

    return devEfficiency
})

// 导出数据
const exportData = async () => {
    if (!historyData.value.length) {
        ElMessage.warning('暂无数据可导出')
        return
    }

    try {
        // 准备导出数据
        const exportData = historyData.value.map(item => {
            const row = {
                '日期': item.create_time,
                '酒店名': item.demand,
                '洗涤量': item.production,
                '详情': item.detail
            }
            return row
        })

        // 创建工作簿
        const wb = XLSX.utils.book_new()
        const ws = XLSX.utils.json_to_sheet(exportData)
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

        // 导出文件
        const fileName = `酒店洗涤量统计_${formatTime(new Date())}.xlsx`
        XLSX.writeFile(wb, fileName)
    } catch (error) {
        ElMessage.error('导出失败')
        console.error('导出失败:', error)
    }
}

// 格式化时间
const formatTime = (time) => {
    return new Date(time).toLocaleString()
}


watch(() => props.dev_ids, async (newVal) => {
    if (newVal.length === 0) {
        return
    }
    // 获取设备名称
    props.dev_ids.forEach(async item => {
        devNameMap.value[item] = (await pdStore.getDevInfo(item)).name
    })
}, { deep: true, immediate: true })


// 初始化
onMounted(async () => {
    const startTime = formatDate(new Date())
    timeValue.value = startTime
})

//转格式化时间,YYYY-MM-DD HH:mm:ss
const formatDate = (date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    const second = date.getSeconds().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}
// 返回上一页
const goBack = () => {
    //返回上一页面
    router.back();
};
</script>

<style scoped>
.history-container {
    padding-top: 10px;
    height: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;
    /* 防止工具栏被压缩 */
}

.buttons {
    padding-left: 20px;
    display: flex;
    gap: 10px;
}


/* 固定表头 */
:deep(.el-table__header-wrapper) {
    position: sticky;
    top: 0;
    z-index: 1;
}

.pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
    /* 防止分页被压缩 */
}

.dev-status-gantt {
    flex: 1;
    min-height: 300px;
    height: 100%;
    /* 确保甘特图占据剩余空间 */
}

.dev-status-gantt-header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
}

.status-colors {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50%;
    height: 20px;
    margin: 0 auto;
}

.status-color {
    display: flex;
    margin-right: 40px;

}

.status-color-bar {
    width: 40px;
    height: 16px;
    margin-left: 10px;
    border-radius: 4px;
}

.dev-efficiency {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50%;
    height: 20px;
    margin: 0 auto 10px auto;
}

.dev-efficiency-item {
    font-weight: 600;
    margin-right: 40px;
}
</style>