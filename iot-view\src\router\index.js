import { createRouter, createWebHistory } from 'vue-router'
import layout from '../layout/layout.vue'

/**
 * @description 📚 路由参数配置简介
 * @param path ==> 路由菜单访问路径
 * @param name ==> 路由 name (对应页面组件 name, 可用作 KeepAlive 缓存标识 && 按钮权限筛选)
 * @param redirect ==> 路由重定向地址
 * @param component ==> 视图文件路径
 * @param meta ==> 路由菜单元信息
 * @param meta.icon ==> 菜单和面包屑对应的图标
 * @param meta.title ==> 路由标题 (用作 document.title || 菜单的名称)
 * @param meta.activeMenu ==> 当前路由为详情页时，需要高亮的菜单
 * @param meta.isLink ==> 路由外链时填写的访问地址
 * @param meta.isHide ==> 是否在菜单中隐藏 (通常列表详情页需要隐藏)
 * @param meta.isFull ==> 菜单是否全屏 (示例：数据大屏页面)
 * @param meta.isAffix ==> 菜单是否固定在标签页中 (首页通常是固定项)
 * @param meta.isKeepAlive ==> 当前路由是否缓存
 * */

// export const errorRouter = [
//   {
//     path: "/403",
//     name: "403",
//     component: () => import("@/components/ErrorMessage/403.vue"),
//     meta: {
//       title: "403页面"
//     }
//   },
//   {
//     path: "/404",
//     name: "404",
//     component: () => import("@/components/ErrorMessage/404.vue"),
//     meta: {
//       title: "404页面"
//     }
//   },
//   {
//     path: "/500",
//     name: "500",
//     component: () => import("@/components/ErrorMessage/500.vue"),
//     meta: {
//       title: "500页面"
//     }
//   },
//   // Resolve refresh page, route warnings
//   {
//     path: "/:pathMatch(.*)*",
//     component: () => import("@/components/ErrorMessage/404.vue"),
//     meta: {
//       title: "404页面"
//     }
//   }
// ]

export const testRouter = [
  {//产品管理
    path: '/product',
    name: 'Product',
    redirect: '/products',
    component: layout,
    meta: { title: "设备管理", icon: "" },
    children: [
      {
        path: '/products',
        name: 'products',
        component: () => import('../views/product/productList.vue'),
        meta: { title: "产品列表", icon: "" },
      },
      {
        path: '/product/:pro_id',
        name: 'product',
        component: () => import('../views/product/productDetail.vue'),
        meta: { title: "产品详情", icon: "", isHide: true },
      },
      {
        path: '/devices',
        name: 'devices',
        component: () => import('../views/product/deviceList.vue'),
        meta: { title: "设备列表", icon: "" },
      },
      {
        path: '/device/:dev_id',
        name: 'device',
        component: () => import('../views/product/deviceDetail.vue'),
        meta: { title: "设备详情", icon: "", isHide: true },
      }
    ]
  },
  {
    path: '/work',
    name: 'WorkSpace',
    redirect: '/devstate',
    component: layout,
    meta: { title: "工作台", icon: "" },
    children: [
      {
        path: '/overview',
        name: 'overview',
        // route level code-splitting
        // this generates a separate chunk (About.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: () => import('../views/overview/overview.vue'),
        meta: {
          title: "设备总览",
          icon: "",          
        }, //控制导航栏是否显示
      },
      {
        path: '/devstate',
        name: 'devstate',
        component: () => import('../views/devState/index.vue'),
        meta: { title: "设备实时状态", icon: "" },
      },
      {
        path: '/bigscreen',
        name: 'bigscreen',
        component: () => import('../views/bigScreen/bigScreenIndex.vue'),
        meta: {
          title: "大屏显示",
          icon: "",
          isFull: true
        },
      }
    ]
  },
  {
    path: '/aggregate',
    name: 'Aggregate',
    redirect: '/',
    component: layout,
    meta: { title: "报表", icon: "" },
    children: [
      {
        path: '/devproduction',
        name: 'devproduction',
        component: () => import('../views/production/devProduction.vue'),
        meta: { title: "设备产量统计", icon: "" }
      },
      {
        path: '/hotelproduction',
        name: 'hotelproduction',
        component: () => import('../views/production/hotelProduction.vue'),
        meta: { title: "酒店产量统计", icon: "" }
      },
      {
        path: '/stateTime',
        name: 'stateTime',
        component: () => import('../views/stateTime/stateTimeChart.vue'),
        meta: { title: "状态时间统计", icon: "" }
      },
      {
        path: '/process',
        name: 'process',
        component: () => import('../views/process/washProcess.vue'),
        meta: { title: "洗涤工艺数据", icon: "" }
      },
      {
        path: '/dailyEenergy',
        name: 'dailyEenergy',
        component: () => import('../views/energy/dailyEenergy.vue'),
        meta: { title: "每天能耗", icon: "" }
      }
    ]
  },
  {
    path: '/rule',
    name: 'rule',
    redirect: '/rule_list',
    component: layout,
    meta: { title: "规则管理", icon: "" },
    children: [
      {
        path: '/rule_add',
        name: 'ruleAdd',
        component: () => import('../views/rule/ruleEdit.vue'),
        meta: { title: "创建规则", icon: "" }
      },
      {
        path: '/rule_list',
        name: 'ruleList',
        component: () => import('../views/rule/ruleList.vue'),
        meta: { title: "规则列表", icon: "" }
      },
      {
        path: '/rule/:rule_id',
        name: 'ruleEdit',
        component: () => import('../views/rule/ruleEdit.vue'),
        meta: { title: "编辑规则", icon: "" }
      },
      {
        path: '/rule_history',
        name: 'ruleHistory',
        component: () => import('../views/rule/ruleHistory.vue'),
        meta: { title: "规则历史", icon: "" }
      }
    ]
  },
  {
    path: '/alarm',
    name: 'alarm',
    redirect: '/alarm_list',
    component: layout,
    meta: { title: "报警管理", icon: "" },
    children: [
      {
        path: '/alarm_list',
        name: 'alarmList',
        component: () => import('../views/alarm/alarmList.vue'),
        meta: { title: "报警列表", icon: "" }
      },
      {
        path: '/alarm_add',
        name: 'alarmAdd',
        component: () => import('../views/alarm/alarmEdit.vue'),
        meta: { title: "创建报警", icon: "" }
      },
      {
        path: '/alarm/:alarm_id',
        name: 'alarmEdit',
        component: () => import('../views/alarm/alarmEdit.vue'),
        meta: { title: "编辑报警", icon: "" }
      }
    ]
  },
  {
    path: '/textile',
    name: 'textile',
    redirect: '/textile/hotels',
    component: layout,
    meta: { title: "布草管理", icon: "" },
    children: [
      {
        path: '/hotels',
        name: 'hotels',
        component: () => import('../views/textile/hotels.vue'),
        meta: { title: "酒店列表", icon: "" }
      },
      {
        path: '/hotel/:hotel_id',
        name: 'hotel',
        component: () => import('../views/textile/hotelDetail.vue'),
        meta: { title: "酒店详情", icon: "" }
      },
      {
        path: '/textiles',
        name: 'textiles',
        component: () => import('../views/textile/textiles.vue'),
        meta: { title: "布草列表", icon: "" }
      },
      {
        path: '/texRecord',
        name: 'texRecord',
        component: () => import('../views/textile/texRecord.vue'),
        meta: { title: "布草收发管理", icon: "" }
      },
    ]
  },
  {
    path: '/user',
    name: 'user',
    redirect: '/user/userinfo',
    component: layout,
    meta: { title: "账户信息", icon: "" },
    children: [
      {
        path: '/userinfo',
        name: 'userinfo',
        component: () => import('../views/user/userInfo.vue'),
        meta: { title: "账户信息", icon: "" }
      },
      {
        path: '/userList',
        name: 'userList',
        component: () => import('../views/user/userList.vue'),
        meta: { title: "用户列表", icon: "" }
      },
      {
        path: '/logout',
        name: 'logout',
        component: () => import('../views/user/logout.vue'),
        meta: { title: "退出", icon: "" }
      },
    ]
  }
]

export const staticRouter = [
  {
    path: '/',
    name: 'home',
    component: layout,
    children: [
      {
        path: '',  // 空路径表示父路由的默认子路由
        name: 'index',
        component: () => import('../views/index.vue'),
      }
    ],
    meta: { title: "首页", icon: "", showParent: true }  // showParent 表示在导航中只显示父级
  },
  {
    path: "/login",
    name: "login",
    component: () => import("../views/login.vue"),
    meta: {
      title: "登录",
      isHide: true
    }
  },
  {
    path: '/logout',
    name: 'logout',
    component: () => import('../views/user/logout.vue'),
    meta: { title: "登出", icon: "", isHide: true }
  }
];

//基础路由，
export const constantRoutes = [...staticRouter, ...testRouter]

//需要根据权限动态变化的路由
export const asyncRoutes = [
  {
    path: '/permission',
    name: 'Permission',
    // route level code-splitting
    // this generates a separate chunk (About.[hash].js) for this route
    // which is lazy-loaded when the route is visited.
    component: layout,
    redirect: '/about',
    meta: {
      title: 'Permission',
      roles: ['admin', 'user'] // you can set roles in root nav
    },
    children: [
      {
        path: '/about',
        name: 'about',
        // route level code-splitting
        // this generates a separate chunk (About.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: () => import('../views/AboutView.vue'),
        meta: {
          title: '有权限页面',
          roles: ['admin'] // you can set roles in root nav
        },
      }
    ]
  },
]

const initRouter = () => createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: constantRoutes
})

const router = initRouter()

export function resetRouter() {
  const newRouter = initRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
