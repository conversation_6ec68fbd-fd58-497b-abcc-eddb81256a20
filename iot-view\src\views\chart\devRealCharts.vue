<!-- 设备实时曲线图 -->
<template>
    <div class="dev-charts">
        <v-chart class="chart" :option="chartOption" autoresize />
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import VChart from 'vue-echarts'
import * as echarts from "echarts"
import { useDataStore } from '@/stores/modules/data'
import { useProductsStore } from '@/stores/modules/products'

const props = defineProps({
    dev_id: {
        type: String,
        required: true
    },
    //图表系列,格式为对象数组
    //[{identifier: 'speed', name: '洗涤速度', type: 'line'},
    // {identifier: 'level', name: '水位', type: 'line'}]
    chart_series: {
        type: Array,
        default: () => []
    },
    //图表名称
    chart_name: {
        type: String,
        default: ''
    }
})

const dataStore = useDataStore()
const pdStore = useProductsStore()
const chartData = ref([])   //属性数据
const chartProps = ref([])    //设备属性

// 获取设备属性定义
const getDeviceChartProps = async (dev_id) => {
    if (!dev_id) return

    // 先从 store 中获取
    let dev_props = await pdStore.getDevProps(dev_id)
    if (!dev_props || !dev_props.length) {
        console.warn(`设备 ${dev_id} 没有属性定义`)
        return []
    }
    // 转换属性定义格式,只保留数值类型的属性
    const number_props = dev_props?.filter(prop => prop.data_type === 'NUMBER')
        .map(prop => ({
            identifier: prop.identifier,
            name: prop.name,
            unit: prop.unit || ''
        })) || []

    console.log('图表属性', number_props)
    return number_props
}

// 计算图表配置
const chartOption = computed(() => ({
    title: {// 图表标题
        text: props.chart_name || props.dev_id + ' 实时数据'
    },
    tooltip: {// 提示框
        show: true,
        trigger: 'item',

    },
    legend: {//曲线的图示
        data: chartProps.value.map(prop => prop.name),
        top: '8%'
    },
    xAxis: {// X轴
        type: 'time'
    },
    yAxis: {// Y轴
        type: 'value'
    },
    dataset: {// 数据集
        dimensions: ['create_time', ...chartProps.value.map(prop => prop.identifier)],
        source: chartData.value?.slice(-60) || []
    },
    series: chartProps.value.map(prop => ({// 数据系列
        type: prop.type || 'line',
        smooth: true,
        name: prop.name,
        encode: {
            x: 'create_time',
            y: prop.identifier
        }
    }))
}))

// 监听后台数据变化
watch(
    () => dataStore.devProps[props.dev_id],
    (newData) => {
        chartData.value = newData
        console.log('图表数据', chartData.value)
    },
    { deep: true, immediate: true }
)

// 监听设备id变化
watch(
    () => props.dev_id,
    async (newId) => {
        if (!newId) return
        if (props.chart_series&&props.chart_series.length) {
            chartProps.value = props.chart_series
        } else {
            chartProps.value = await getDeviceChartProps(newId)
        }        
        chartData.value = dataStore.devProps[newId]
    },
    { immediate: true }
)
</script>

<style scoped>
.dev-charts {
    width: 100%;
    height: 100%;
}

.chart {
    width: 100%;
    height: 400px;
}
</style>
