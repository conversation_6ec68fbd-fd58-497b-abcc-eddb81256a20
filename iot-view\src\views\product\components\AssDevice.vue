<template>
    <div>
        <el-button type="primary" @click="fetchAssDevice">刷新</el-button>
        <el-button type="primary" @click="editDevice({})">新增</el-button>
        <el-table style="margin-top: 20px;" :data="assdevices">
            <el-table-column prop="name" label="设备名称"></el-table-column>
            <el-table-column prop="dev_id" label="设备ID" width="180"></el-table-column>
            <el-table-column prop="cus_name" label="所属客户" width="180"></el-table-column>
            <el-table-column prop="lastOnlineTime" label="最后在线时间" width="180"></el-table-column>
            <el-table-column label="操作" width="180">
                <template #default="scope">
                    <el-button type="primary" @click="editDevice(scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 编辑设备 -->
        <el-dialog v-model="editDeviceDialog" title="新增设备" width="50%" destroy-on-close>
            <device-edit :pro_id="props.pro_id" :dev_id="currentDevice.dev_id" @success="handleEditSuccess" />
        </el-dialog>
    </div>
</template>

<!-- 获取产品关联的设备 -->
 
<script setup>
import { ref, onMounted,watch } from 'vue';
import { ElTable, ElTableColumn, ElButton } from 'element-plus';
import { getAssDevsApi } from '@/api/products';
import { getDevsLastOnlineTimeApi } from '@/api/data';
import deviceEdit from '../deviceEdit.vue'

const props = defineProps({
    pro_id: {
        type: String,
        required: true
    }
});

const assdevices = ref([]);
const currentDevice=ref({})
// 获取设备的属性列表
const fetchAssDevice = async () => {
    assdevices.value = await getAssDevsApi(props.pro_id);    
};


//获取最后在线时间
const getLastOnlineTime = async () => {
    const dev_ids = assdevices.value.map(item => item.dev_id);
    if (dev_ids.length === 0) {
        return;
    }
    const lastOnlineTime = await getDevsLastOnlineTimeApi({dev_ids:dev_ids});
    assdevices.value.forEach(item => {
        item.lastOnlineTime = lastOnlineTime.find(item => item.dev_id === item.dev_id)?.last_online_time || '--';
    });    
}
watch(() => props.pro_id, async (newVal) => {
    if (newVal) {
        await fetchAssDevice();
        await getLastOnlineTime();
    }
}, { immediate: true });

const editDeviceDialog=ref(false)
//编辑设备
const editDevice = (row) => {
    currentDevice.value=row
    editDeviceDialog.value=true
}
const handleEditSuccess= async()=>{
    editDeviceDialog.value=false
    device.value = await pdStore.requestDeviceDetail(dev_id.value)    
}

</script>

<style scoped></style>