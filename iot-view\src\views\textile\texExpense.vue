<template>
    <!-- 月布草洗涤费明细 -->
    <div class="history-container">
        <!-- 标题栏 -->
        <el-page-header @back="goBack">
            <template #content>
                <span class="text-large font-600 mr-3">月布草洗涤费明细 </span>
            </template>
        </el-page-header>
        <el-divider style="margin: 20px 0px 10px 0px;" />
        <!-- 时间选择和操作按钮 -->
        <div class="toolbar">
            <el-date-picker v-model="timeRange" type="datetimerange" range-separator="至" start-placeholder="开始时间"
                end-placeholder="结束时间" :default-time="defaultTime" value-format="YYYY-MM-DD HH:mm:ss"
                />
            <div class="buttons">
                <!-- 查看数据曲线 -->
                <el-button type="primary" @click="getTexSettlement">查询明细</el-button>
                <el-button type="success" :icon="Download" @click="exportData" disabled>导出</el-button>
            </div>
        </div>

        <!-- 数据表格 -->
        <el-table ref="tableRef" :data="tableData" style="height:calc(100vh - 200px)" border v-loading="loading"
            show-summary sum-text="布草费用合计" :row-style="rowStyle">
            <!-- 按时间筛选、排序 -->
            <el-table-column prop="create_time" label="时间" width="180" fixed="left" :sortable="true" />            
            <el-table-column v-for="tex in texs" :key="tex.tex_id" :prop="tex.tex_id"
                :label="tex.name+'('+tex.price+'元/件)' " align="center">                                 
                <template #default="{ row }">
                    {{ row[tex.tex_id] }}
                </template>
            </el-table-column>
            <el-table-column prop="total" label="洗涤费小计" width="180" fixed="right" />            
        </el-table>

    </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, TrendCharts } from '@element-plus/icons-vue'
import { getTextilesApi,getTexRecordsApi } from '@/api/textile'
import { getTexsProductionApi } from '@/api/aggregate'
import router from '@/router'


const props = defineProps({
    hotel_id: {
        type: String,
        required: true
    }    
})

const tableRef = ref(null)
const timeRange = ref([])
const tableData = ref([])
const loading = ref(false)
const hotel_name=ref('')
const texs=ref([])

const defaultTime = [
    new Date(2025, 1, 1, 0, 0, 0),
    new Date(2025, 1, 1, 23, 59, 59)
]

// 获取纺织品列表
const fetchTexsData = async () => {
    loading.value = true
    let d={}
    if (props.hotel_id) {
        d.hotel_ids = [props.hotel_id]
    }
    console.log('hotel_id', props.hotel_id)
    try {
        texs.value = await getTextilesApi(d)  
        hotel_name.value = texs.value[0].hotel_name        
    } catch (error) {
        ElMessage.error('获取布草列表失败')
    } finally {
        loading.value = false
    }
}
// 监听酒店id
watch(()=>props.hotel_id,async()=>{
    await fetchTexsData()    
},{immediate:true})

//获取布草送货记录
const fetchTexRecords = async () => {
    let d = { start_time: timeRange.value[0], end_time: timeRange.value[1] }
    if (props.hotel_id) {
        d.hotel_ids = [props.hotel_id],
        d.type='OUT'
    }
    const res = await getTexRecordsApi(d)
        
    return res||[]
}

//数据按天合并
const getTableData=(data)=>{
    const resultMap = {};
    // 遍历原始数据,data是一个对象数组,转为一个按时间合并的对象数组
    data.forEach(item=>{
        // 把create_time转换为YYYY-MM-DD
        const { create_time, tex_id, quantity } = item;
        const create_time_str = create_time.split(' ')[0];
        // 如果当前时间点不存在，初始化一个对象
        if (!resultMap[create_time_str]) {
            // 把create_time转换为YYYY-MM-DD            
            resultMap[create_time_str] = { create_time: create_time_str };
        }
        // 将 tex_id 作为键，production 作为值，添加到对应时间点的对象中
        resultMap[create_time_str][tex_id] = quantity;
    })

    // 将结果转换为数组  
    //console.log('按天合并的洗涤量', resultMap)
    return Object.values(resultMap)
}

//计算洗涤费小计
const getTotal=(data)=>{
    const total=[]    
    data.forEach(item=>{
        item.total=0
        texs.value.forEach(tex=>{
            //如果item[tex.tex_id]为空,则不计算
            if(item[tex.tex_id]){
                item.total+=Math.round(item[tex.tex_id]*tex.price)
            }
        })
        total.push(item)
    })  
    return total  
}
//获取布草赔偿记录
const fetchTexPayments = async () => {
    let d = { start_time: timeRange.value[0], end_time: timeRange.value[1] }
    if (props.hotel_id) {
        d.hotel_ids = [props.hotel_id],
        d.type='PAY'
    }
    const res = await getTexRecordsApi(d)
    return res||[]
}

const getTexPayments=(data)=>{
    const total=[]    
    data.forEach(item=>{
        item.total=0
        texs.value.forEach(tex=>{
            //如果item[tex.tex_id]为空,则不计算
            if(item[tex.tex_id]){
                item.total-=Math.round(item[tex.tex_id]*tex.pay_price)
                item[tex.tex_id]=item[tex.tex_id]+' 赔付'
            }
        })
        total.push(item)
    })  
    return total  
}

const getSummary=(param)=>{
    const {columns,data}=param
    columns.forEach((column,index)=>{
        //column.property

    })
    
    return summary
}

// 导出数据
const exportData = async () => {
    if (!tableData.value.length) {
        ElMessage.warning('暂无数据可导出')
        return
    }

    try {
        // 准备导出数据


        // 创建工作簿
        const wb = XLSX.utils.book_new()
        const ws = XLSX.utils.json_to_sheet(tableData.value)
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

        // 导出文件
        const fileName = `${hotel_name.value}_布草洗涤费明细_${timeRange.value[0].split(' ')[0]}_${timeRange.value[1].split(' ')[0]}.xlsx`
        XLSX.writeFile(wb, fileName)
    } catch (error) {
        ElMessage.error('导出失败')
        console.error('导出失败:', error)
    }
}

// 格式化时间
const formatTime = (time) => {
    return new Date(time).toLocaleString()
}

const getTexSettlement = async () => {//获取布草费用结算数据    
    const res = await fetchTexRecords()
    console.log('布草洗涤量', res)

    const dayProduction = getTableData(res) || [] 
    console.log('按天合并的洗涤量', dayProduction)
    const total = getTotal(dayProduction)
    console.log('洗涤费小计', total)
    const payments = await fetchTexPayments()
    const dayPayments = getTableData(payments) || []
    const totalPayments = getTexPayments(dayPayments)
    console.log('赔偿费小计', totalPayments)
    //合并洗涤费和赔偿费

    tableData.value = [...total, ...totalPayments]
}

// 行样式
const rowStyle = ({ row,rowIndex }) => {
    if (row.total < 0) {
        return {color:'red'}
    }
}

// 初始化
onMounted(async () => {
    // 设置默认时间范围为最近24小时
    const end = new Date()
    const start = new Date()
    start.setDate(start.getDate() - 1)
    const endTime = formatDate(end)
    const startTime = formatDate(start)
    timeRange.value = [startTime, endTime]
})

//转格式化时间,YYYY-MM-DD HH:mm:ss
const formatDate = (date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    const second = date.getSeconds().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}
// 返回上一页
const goBack = () => {
    //返回上一页面
    router.back();
};
</script>

<style scoped>
.history-container {
    padding-top: 10px;
    height: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;
    /* 防止工具栏被压缩 */
}

.buttons {
    padding-left: 20px;
    display: flex;
    gap: 10px;
}


/* 固定表头 */
:deep(.el-table__header-wrapper) {
    position: sticky;
    top: 0;
    z-index: 1;
}

.pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
    /* 防止分页被压缩 */
}


</style>

