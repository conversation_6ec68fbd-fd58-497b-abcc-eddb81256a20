<!-- 大屏显示 -->
<template>
    <!-- 横向三区布置 -->
    <div class="bg">
        <div class="header">
            <!-- 这是标题栏及菜单栏 -->
            <h1 class="title">大屏监控系统</h1>
            <div class="header-info">
                <span>当前时间：{{ nowTime }}</span>
                <socketStatus />                
            </div>
        </div>
        <div class="content">
            <!-- 这是主页面，放置多个组件 -->
            <div class="main-container">
                <!-- 布置区域,三列 -->
                <!-- 左侧第一列 -->
                <div class="left-column">
                    <!-- 左侧第一列的内容 -->
                    <div class="component-card leftTop "> 
                        <div class="card-header">设备状态</div>                       
                        <div class="card-content">
                            <!-- 组件1内容 -->    
                             <DevStatusPie :dev_ids="dev_ids" />                            
                        </div>
                    </div>
                    <div class="component-card leftBottom"> 
                        <div class="card-header">产量趋势图</div>                       
                        <div class="card-content">
                            <!-- 组件2内容 -->                             
                            <TotalProduction :dev_ids_1="wsh_ids" :dev_ids_2="dry_ids" :dev_ids_3="fod_ids" :time_range="timeRange" :series_name="['水洗机','烘干机','后整理']" />
                        </div>
                    </div>
                </div>

                <div class="center-column">
                    <!-- 中间第二列 -->
                    <div class="component-card top">
                        <div class="card-header">设备分布图</div>
                        <div class="card-content">
                            <!-- 组件3内容 -->
                             
                        </div>
                    </div>
                    <div class="component-card bottom">
                        <div class="card-header">产量趋势</div>
                        <div class="card-content">
                            <!-- 组件4内容 -->
                        </div>
                    </div>
                </div>

                <div class="right-column">
                    <!-- 右侧第三列 -->
                    <div class="component-card top">
                        <div class="card-header">运行状态</div>
                        <div class="card-content">
                            <!-- 组件5内容 -->
                        </div>
                    </div>
                    <div class="component-card bottom">
                        <div class="card-header">数据统计</div>
                        <div class="card-content">
                            <!-- 组件6内容 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer">
            <!-- 这是底部栏 -->
             <div style="position: absolute;right: 20px;" @click="goIndex">
                返回
             </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import DevStatusPie from '@/views/chart/devStatusPie.vue';
import DevsProductionBar from '@/views/chart/devsProductionBar.vue';
import TotalProduction from '@/views/chart/totalProduction.vue';
import socketStatus from '../chart/socketStatus.vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const nowTime = ref('')
let timer = null
const dev_ids = ['WSH25001', 'WSH25002', 'WSH25003', 'FOD25001', 'FOD25002', 'DRY25001', 'DRY25002']
const wsh_ids = ['WSH25001', 'WSH25002']
const dry_ids = ['DRY25001', 'DRY25002']
const fod_ids = ['FOD25001', 'FOD25002']
const dev_infos = ref([
    { name: '水洗机1号', identifier: 'WSH25001' },
    { name: '水洗机2号', identifier: 'WSH25002' },
])

// 更新时间
const updateTime = () => {
    const now = new Date()
    nowTime.value = now.toLocaleString()
}
const timeRange = ref(['2025-03-10 00:00:00', "2025-03-10 14:59:59"])

const goIndex=()=>{
    router.push('/')
}
onMounted(() => {
    updateTime()
    timer = setInterval(updateTime, 1000)
})

onUnmounted(() => {
    if (timer) {
        clearInterval(timer)
    }
})
</script>

<style scoped>
.bg {
    height: 100vh;
    width: 100%;
    background-color: #020308;
    background-image: url('../../../public/images/bg.png');
    background-size: cover;
    background-position: center center;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.header {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 50%, rgba(0, 0, 0, 0.3) 100%);
    border-bottom: 2px solid rgba(64, 158, 255, 0.3);
}

.title {
    color: #ffffff;
    font-size: 32px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
    margin: 0;
}
.header-info{
    position: absolute;
    right: 20px;    
    align-items: center;
    gap: 20px;
    color: #FAFAFA;
}

.content {
    flex: 1;
    padding: 10px;    
    height: 100%;
    overflow: hidden;
}

.main-container {
    height: 100%;
    display: flex;
    gap: 10px;
}

.left-column {
    flex: 0.8;
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
}

.center-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
}

.right-column {
    flex: 0.6;
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
}

.leftTop {
    flex: 0.6 !important;
    border: 1px solid rgba(64, 158, 255, 0.3);
    border-radius: 8px;
    transition: all 0.3s ease;
    
}

.leftBottom {
    flex: 1 !important;
    border: 1px solid rgba(64, 158, 255, 0.3);
    border-radius: 8px;
    
}

.component-card {
    flex: 1;    
    border: 1px solid rgba(64, 158, 255, 0.3);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;    
    transition: all 0.3s ease;
}

.component-card:hover {
    border-color: rgba(64, 158, 255, 0.6);
    box-shadow: 0 6px 30px rgba(64, 158, 255, 0.2);
    transform: translateY(-2px);
}

.card-header {
    height: 40px;
    background: linear-gradient(90deg, rgba(64, 158, 255, 0.2) 0%, rgba(64, 158, 255, 0.1) 100%);
    border-bottom: 1px solid rgba(64, 158, 255, 0.3);
    display: flex;
    align-items: center;
    padding: 0 20px;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.card-content { 
    flex: 1;
    padding: 10px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    height: 100%;
}

.footer {
    height: 60px;
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 50%, rgba(0, 0, 0, 0.3) 100%);
    border-top: 2px solid rgba(64, 158, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.footer-info {
    display: flex;
    gap: 40px;
    color: #ffffff;
    font-size: 14px;
}

.footer-info span {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}


</style>
