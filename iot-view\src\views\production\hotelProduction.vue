<template>
    <!-- 酒店生产数据表格 -->
    <div class="history-container">
        <!-- 标题栏 -->
        <el-page-header @back="goBack">
            <template #content>
                <span class="text-large font-600 mr-3">酒店洗涤量统计 </span>
            </template>
        </el-page-header>
        <el-divider style="margin: 20px 0px 10px 0px;" />
        <!-- 时间选择和操作按钮 -->
        <div class="toolbar">
            <el-date-picker v-model="timeRange" type="datetimerange" range-separator="至" start-placeholder="开始时间"
                end-placeholder="结束时间" :default-time="defaultTime" value-format="YYYY-MM-DD HH:mm:ss"
                @change="fetchHistoryData" />
            <div class="buttons">
                <!-- 查看数据曲线 -->
                <el-button type="primary" :icon="TrendCharts" @click="showHistoryChart">查看曲线</el-button>
                <el-button type="success" :icon="Download" @click="exportData">导出</el-button>
            </div>
        </div>

        <!-- 数据表格 -->
        <el-table ref="tableRef" :data="tableData" style="height:calc(100vh - 200px)" border v-loading="loading"
            show-summary sum-text="洗涤量合计">
            <!-- 按时间筛选、排序 -->
            <el-table-column prop="create_time" label="时间" width="180" fixed="left" :sortable="true" />
            <el-table-column v-for="hotel in hotelInfo" :key="hotel.hotel_id" :prop="hotel.hotel_id"
                :label="hotel.name" align="center">
                <template #default="{ row }">
                    {{ row[hotel.hotel_id] }}
                </template>
            </el-table-column>
        </el-table>
        <!-- 数据曲线 -->
        <el-dialog v-model="historyChartDialog" width="50%" height="50%">
            <HistoryChart :data="chartData" :chart_series="chartSeries" chart_name="酒店每天产量" />
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Download, TrendCharts } from '@element-plus/icons-vue'
import { getHotelsProductionApi } from '@/api/aggregate'
import { getHotelsApi } from '@/api/textile'
import HistoryChart from '@/views/chart/historyChart.vue'
import router from '@/router'


const props = defineProps({
    dev_ids: {
        type: Array,
        default: ['WSH25001','WSH25002','WSH25003'],
        required: true
    },
    hotel_ids: {
        type: Array,
        default: null,
    }
})
const tableRef = ref(null)
const timeRange = ref([])
const tableData = ref([])
const loading = ref(false)

const totalCount = ref(0)
const defaultTime = [
    new Date(2025, 1, 1, 0, 0, 0),
    new Date(2025, 1, 1, 23, 59, 59)
]

//获取酒店信息
const hotelInfo = ref([])
const getHotelInfo = async () => {
    const res = await getHotelsApi({hotel_ids: props.hotel_ids})
    hotelInfo.value = res||[]
}
// 获取每天产量数据
const fetchHistoryData = async () => {
    if (!timeRange.value || !timeRange.value[0] || !timeRange.value[1]) {
        ElMessage.warning('请选择时间范围')
        return
    }

    loading.value = true
    const hotel_ids = hotelInfo.value.map(item=>item.hotel_id)
    try {
        const [startTime, endTime] = timeRange.value
        const params = {
            dev_ids: props.dev_ids,
            hotel_ids: hotel_ids,
            //时间转字符串格式化
            start_time: startTime,
            end_time: endTime,
        }
        const res = await getHotelsProductionApi(params)
        //重置表格的筛选
        tableRef.value.clearFilter()
        //重置排序
        tableRef.value.clearSort()
        tableData.value = mergeDataByDay(res) || []
    } catch (error) {
        ElMessage.error('获取历史数据失败')
        console.error('获取历史数据失败:', error)
    } finally {
        loading.value = false
    }
}

//数据按天合并
const mergeDataByDay = (data) => {
    const resultMap = {};
    // 遍历原始数据,data是一个对象数组,转为一个按时间合并的对象数组
    data.map(item=>{
        // 把create_time转换为YYYY-MM-DD
        const { create_time, hotel, production } = item;
        // 把create_time转换为YYYY-MM-DD
        const key = create_time.split(' ')[0]
        // 如果当前时间点不存在，初始化一个对象
        if (!resultMap[key]) {
            resultMap[key] = { create_time: key };
        }
        // 将 dev_id 作为键，production 作为值，添加到对应时间点的对象中
        resultMap[key][hotel] = production;
    })
    let result = Object.values(resultMap)
    //按时间排序
    result.sort((a, b) => {
        return new Date(a.create_time) - new Date(b.create_time)
    })

    // 将结果转换为数组    
    return result

}

const filterData = (value, row, column) => {
    // 筛选数据
    const property = column['property']
    return row[property] === value
}

// 查看数据曲线
const historyChartDialog = ref(false)
const chartData = ref([])


const showHistoryChart = () => {
    chartData.value = tableData.value
    // 弹出对话框
    historyChartDialog.value = true
}


const chartSeries = computed(() => {
    // 把chartData中的create_time转换为YYYY-MM-DD
    return hotelInfo.value.map(item => {
        return {
            name: item.name,
            identifier: item.hotel_id,
            type: 'bar'
        }
    })
})

// 导出数据
const exportData = async () => {
    if (!historyData.value.length) {
        ElMessage.warning('暂无数据可导出')
        return
    }

    try {
        // 准备导出数据
        const exportData = historyData.value.map(item => {
            const row = {
                '日期': item.create_time,
                '酒店名': item.hotel,  
                '洗涤量': item.production,
                '详情': item.detail
            }
            return row
        })

        // 创建工作簿
        const wb = XLSX.utils.book_new()
        const ws = XLSX.utils.json_to_sheet(exportData)
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

        // 导出文件
        const fileName = `酒店洗涤量统计_${formatTime(new Date())}.xlsx`
        XLSX.writeFile(wb, fileName)
    } catch (error) {
        ElMessage.error('导出失败')
        console.error('导出失败:', error)
    }
}

// 格式化时间
const formatTime = (time) => {
    return new Date(time).toLocaleString()
}



// 初始化
onMounted(async () => {
    // 设置默认时间范围为最近24小时
    const end = new Date()
    const start = new Date()
    start.setDate(start.getDate() - 1)

    const endTime = formatDate(end)
    const startTime = formatDate(start)

    timeRange.value = [startTime, endTime]
    await getHotelInfo()

})

//转格式化时间,YYYY-MM-DD HH:mm:ss
const formatDate = (date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    const second = date.getSeconds().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}
// 返回上一页
const goBack = () => {
    //返回上一页面
    router.back();
};
</script>

<style scoped>
.history-container {
    padding-top: 10px;
    height: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;
    /* 防止工具栏被压缩 */
}

.buttons {
    padding-left: 20px;
    display: flex;
    gap: 10px;
}


/* 固定表头 */
:deep(.el-table__header-wrapper) {
    position: sticky;
    top: 0;
    z-index: 1;
}

.pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
    /* 防止分页被压缩 */
}
</style>