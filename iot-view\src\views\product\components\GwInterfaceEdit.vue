<template>
    <div class="form-container">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" label-position="right">
            <el-form-item label="设备名称" prop="dev_name">
                <el-select v-model="formData.dev_id" placeholder="请选择设备" style="width: 100%">
                    <el-option v-for="item in deviceList" :key="item.dev_id" :label="item.name + ' - ' + item.dev_id " :value="item.dev_id" />
                </el-select>
            </el-form-item>
            <el-form-item label="接口类型" prop="interface">
                <el-select v-model="formData.interface" placeholder="请选择接口类型" style="width: 100%">
                    <el-option label="网口" value="ETH" />
                    <el-option label="RS485" value="RS485" />
                    <el-option label="RS232" value="RS232" />
                </el-select>
            </el-form-item>
            <el-form-item label="IP地址" prop="ip" v-if="formData.interface === 'ETH'">
                <el-input v-model="formData.ip" placeholder="请输入IP地址" />
            </el-form-item>
            <el-form-item label="端口" prop="port" v-if="formData.interface === 'ETH'">
                <el-input-number v-model="formData.port" :min="1" :max="65535" style="width: 100%" />
            </el-form-item>
            <el-form-item label="波特率" prop="baudrate" v-if="formData.interface === 'RS485'">
                <el-select v-model="formData.baudrate" placeholder="请选择波特率" style="width: 100%">
                    <el-option label="9600" value="9600" />
                    <el-option label="19200" value="19200" />
                    <el-option label="38400" value="38400" />
                    <el-option label="115200" value="115200" />
                </el-select>
            </el-form-item>
            <el-form-item label="数据位" prop="databits" v-if="formData.interface === 'RS485'">
                <el-select v-model="formData.databits" placeholder="请选择数据位" style="width: 100%">
                    <el-option label="8" value="8" />
                    <el-option label="7" value="7" />
                </el-select>
            </el-form-item>
            <el-form-item label="校验位" prop="parity" v-if="formData.interface === 'RS485'">
                <el-select v-model="formData.parity" placeholder="请选择校验位" style="width: 100%">
                    <el-option label="NONE" value="NONE" />
                    <el-option label="ODD" value="ODD" />
                    <el-option label="EVEN" value="EVEN" />
                </el-select>
            </el-form-item>
            <el-form-item label="停止位" prop="stopbits" v-if="formData.interface === 'RS485'">
                <el-select v-model="formData.stopbits" placeholder="请选择停止位" style="width: 100%">
                    <el-option label="1" value="1" />
                    <el-option label="2" value="2" />
                </el-select>
            </el-form-item>
        </el-form>
        <div class="form-footer">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit">{{ submitButtonText }}</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue'
import { getDevsApi, updateInterfaceApi,getInterfaceDetailApi } from '@/api/products'

const props = defineProps({
    gw_id: {
        type: String,
        required: true
    },
})

const isEdit = ref(false)
const interf_id = ref('')
const deviceList = ref([])
const emit = defineEmits(['update', 'cancel'])
const formRef = ref(null)
const formData = ref({dev_name: '',
        dev_id: '',        
        interface: 'ETH',
        ip: '',
        port: 502,
        baudrate: '9600',
        databits: '8',
        parity: 'NONE',
        stopbits: '1'})
const open = async ({ interface_id = '', data = {} } = {}) => {    
    isEdit.value = false
    // 重置表单
    formData.value = {
        dev_name: '',
        dev_id: '',        
        interface: 'ETH',
        ip: '',
        port: 502,
        baudrate: '9600',
        databits: '8',
        parity: 'NONE',
        stopbits: '1'
    }

    if (interface_id) {
        interf_id.value = interface_id
        isEdit.value = true
        const res = await getInterfaceDetailApi(interface_id)
        console.log('接口详情', res)
        formData.value = { ...res }
    }
}


// 修改提交按钮文本
const submitButtonText = computed(() => isEdit.value ? '更新接口' : '创建接口')

// 页面标题计算属性
const pageTitle = computed(() => isEdit.value ? '编辑接口' : '创建接口')

// 表单验证规则
const rules = {
    dev_id: [
        { required: true, message: '请输入设备ID', trigger: 'blur' }
    ],
    interface: [
        { required: true, message: '请选择接口类型', trigger: 'change' }
    ],
    ip: [
        { required: true, message: '请输入IP地址', trigger: 'blur' },
        { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '请输入正确的IP地址', trigger: 'blur' }
    ],
    port: [
        { required: true, message: '请输入端口号', trigger: 'blur' }
    ]
}

//获取设备列表
const getDeviceList = async () => {
    const res = await getDevsApi()
    console.log('设备列表', res)
    //过滤出连接类型是子设备的
    deviceList.value = res.filter(item => item.connect_type === 'SUBDEV')
}

// 取消按钮处理
const handleCancel = () => {
    emit('cancel')
}

// 提交按钮处理
const handleSubmit = async () => {
    try {
        await formRef.value?.validate()   
        //整理数据格式
        const interfaceData = {
            device: formData.value.dev_id,
            gateway: props.gw_id,
            interface: formData.value.interface,
            ip: formData.value.ip,
            port: formData.value.port,
            baudrate: formData.value.baudrate,
            databits: formData.value.databits,
            parity: formData.value.parity,
            stopbits: formData.value.stopbits
        }
        if (isEdit.value) {
            interfaceData.id = interf_id.value
        }
        console.log('表单数据', interfaceData)
        //await updateInterfaceApi(interfaceData)
        emit('update')        
    } catch (error) {
        ElMessage.error(isEdit.value ? '更新失败' : '添加失败')
        console.error('操作失败:', error)
    }
}

// 暴露方法给父组件
defineExpose({
    open,
    validate: () => formRef.value?.validate(),
    resetFields: () => formRef.value?.resetFields()
})


onMounted(async () => {
    await getDeviceList()    
})

</script>

<style scoped>
.form-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding-top: 20px;
    border-top: 1px solid #dcdfe6;
}
</style>