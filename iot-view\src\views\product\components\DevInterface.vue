<template>
    <div class="interface-container">
        <!-- 网关信息 -->        
            <div class="section-header">
                <h3>网关信息</h3>
                <div class="header-desc">
                    设备所连接的网关信息
                </div>
            </div>
            <el-descriptions :column="2" border>
                <el-descriptions-item label="网关ID">
                    {{ interfaceInfo.gw_id || '未连接' }}                    
                </el-descriptions-item>      
                <el-descriptions-item label="网关名称">
                    {{ interfaceInfo.gw_name || '未连接' }}
                </el-descriptions-item>                
                <el-descriptions-item label="接口类型">
                    <el-tag>{{ interfaceInfo.interface || 'TCP' }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="IP地址">
                    {{ interfaceInfo.ip || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="端口">
                    {{ interfaceInfo.port || '-' }}
                </el-descriptions-item> 
                <el-descriptions-item label="波特率">
                    {{ commParams.baudrate || '9600' }}
                </el-descriptions-item>
                <el-descriptions-item label="数据位">
                    {{ commParams.databits || '8' }}
                </el-descriptions-item>
                <el-descriptions-item label="校验位">
                    {{ commParams.parity || 'NONE' }}
                </el-descriptions-item>
                <el-descriptions-item label="停止位">
                    {{ commParams.stopbits || '1' }}
                </el-descriptions-item>               
            </el-descriptions>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getDevInterfaceApi } from '@/api/products';

const props = defineProps({
    dev_id: {
        type: String,
        required: true
    }
})

// 接口信息
const interfaceInfo = ref({
    type: 'TCP',
    ip: '',
    port: '',
    slave_addr: ''
})

// 通讯参数
const commParams = ref({
    baudrate: '9600',
    databits: '8',
    parity: 'NONE',
    stopbits: '1'
})

// 获取设备接口信息
const fetchDeviceInterface = async () => {
    try {
        const data = await getDevInterfaceApi(props.dev_id)
        interfaceInfo.value = data
    } catch (error) {
        console.error('获取设备接口信息失败:', error)
    }
}

onMounted(() => {
    fetchDeviceInterface()
})
</script>

<style scoped>
.interface-container {
    margin-bottom: 30px;
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    
}

.section {
    margin-bottom: 30px;
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.section-header {
    margin-bottom: 20px;
}

.section-header h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: bold;
}

.header-desc {
    color: #666;
    font-size: 14px;
}

:deep(.el-descriptions) {
    margin-top: 10px;
}

:deep(.el-descriptions__cell) {
    padding: 12px 20px;
}

:deep(.el-tag) {
    text-transform: uppercase;
}
</style>
