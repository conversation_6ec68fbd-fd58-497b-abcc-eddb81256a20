<template>
  <div class="nav-menu" :class="{ 'is-collapse': isCollapse }">
    <div class="logo-container">
      <div class="collapse-btn" @click="toggleCollapse">
        <el-icon size="30">
          <Fold v-if="!isCollapse" />
          <Expand v-else />
        </el-icon>
      </div>
      <span v-show="!isCollapse">洗涤云平台</span>

    </div>
    <el-menu :default-active="router.currentRoute.value.fullPath" class="el-menu-vertical" :collapse="isCollapse"
      :unique-opened="true" background-color="#ffffff" text-color="#333333" active-text-color="#409eff"
      @select="handleSelect" router>
      <template v-for="(item, index) in store.routes">
        <template v-if="!item.meta?.isHide">
          <el-sub-menu :index="item.path + ''" :key="index" v-if="item.children && !item.meta?.showParent">
            <template #title>
              <el-icon>
                <component :is="item.meta.icon || 'Menu'" />
              </el-icon>
              <span>{{ item.meta.title }}</span>
            </template>
            <template v-for="(child, index) in item.children" :key="index">
              <el-menu-item v-if="!child.meta?.isHide" :index="child.path + ''">
                <el-icon>
                  <component :is="child.meta?.icon || 'Menu'" />
                </el-icon>
                <span>{{ child.meta?.title || item.meta.title }}</span>
              </el-menu-item>
            </template>
          </el-sub-menu>
          <el-menu-item :index="item.path" v-else>
            <el-icon>
              <component :is="item.meta.icon || 'Menu'" />
            </el-icon>
            <span>{{ item.meta.title }}</span>
          </el-menu-item>
        </template>
      </template>
    </el-menu>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAccount } from '../stores/modules/account'
import { Fold, Expand, Menu } from '@element-plus/icons-vue'


const store = useAccount()
const router = useRouter()
const isCollapse = ref(false)

const handleSelect = (key, keyPath) => {
  console.log(key, keyPath)
}

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<style scoped>
.nav-menu {
  height: 100%;  
  transition: width 0.3s;
  position: relative;
}

.nav-menu.is-collapse {
  width: 64px;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 10px;
  color: #fff;
}

.logo {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.el-menu-vertical {
  border-right: none;
}



.collapse-btn {
  right: 0;
  margin-right: 10px;
  margin-left: 10px;
}

.collapse-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
</style>