import request from '@/utils/request'


//获取产品列表，无需参数
export function getProductsApi(proId) {
  return request({
    url: '/products/getProducts/',
    method: 'get',
    params: { pro_id: proId }
  })
}

//获取单个产品
export function getProductApi(proId) {
  return request({
    url: '/products/getProducts/',
    method: 'get',
    params: { pro_id: proId }
  })
}

//更新产品信息
export function updateProductApi(data) {
  //把data对象改为formdata格式  
  const formData = new FormData()
  for (const key in data) {
    formData.append(key, data[key]) 
  }
  return request({
    url: '/products/updateProduct/',
    method: 'post',
    data: formData
  })
}

//获取设备列表，无需参数，根据客户自动查询
export function getDevsApi() {
  return request({
    url: '/products/getDevices/',
    method: 'get'
  })
}

//获取单个设备
export function getDevDetailApi(dev_id) {
  return request({
    url: '/products/getDevDetail/',
    method: 'get',
    params: { dev_id: dev_id }
  })
}

//更新设备信息
export function updateDevApi(data) {
  //把data对象改为formdata格式  
  const formData = new FormData()
  for (const key in data) {
    formData.append(key, data[key]) 
  }
  return request({
    url: '/products/updateDevice/',
    method: 'post',
    data: formData
  })
}

//获取设备的token
export function getDevTokenApi(dev_id) {
  return request({
    url: '/products/getDevToken/',
    method: 'get',
    params: { dev_id: dev_id }
  })
}

//获取关联设备列表，需产品id
export function getAssDevsApi(pro_id) {
  return request({
    url: '/products/getProdDevs/',
    method: 'get',
    params: { pro_id: pro_id }
  })
}


//获取网关接口信息
export function getGwInterfaceApi(gateway_id) {
  return request({
    url: '/products/getGwInterface/',
    method: 'get',
    params: { gateway_id: gateway_id }
  })
}

//获取设备接口信息
export function getDevInterfaceApi(dev_id) {
  return request({
    url: '/products/getDevInterface/',
    method: 'get',
    params: { dev_id: dev_id }
  })
}
//获取产品接口信息
export function getInterfaceDetailApi(interface_id) {
  return request({
    url: '/products/getInterfaceDetail/',
    method: 'get',
    params: { interface_id: interface_id }
  })
}
//更新接口信息
export function updateInterfaceApi(data) {
  //把data对象改为formdata格式  
  const formData = new FormData()
  for (const key in data) {
    formData.append(key, data[key])
  }
  return request({
    url: '/products/updateInterface/',
    method: 'post',
    data: formData
  })
}

//删除接口信息
export function deleteInterfaceApi(interface_id) {
  const formData = new FormData()
  formData.append('interface_id', interface_id)
  return request({
    url: '/products/deleteInterface/',
    method: 'post',
    data: formData
  })
}
//获取产品属性列表
export function getProductPropsApi(pro_id) {  
  return request({
    url: '/products/getProductProps/',
    method: 'get',
    params: { pro_id: pro_id }
  })
}

//获取设备属性列表
export function getDevPropsApi(dev_id) {
  return request({
    url: '/products/getDevProps/',
    method: 'get',
    params: { dev_id: dev_id }
  })
}

//添加或更新产品属性
export function updateProductPropApi(data) {
  //把data对象改为formdata格式  
  const formData = new FormData()
  for (const key in data) {
    formData.append(key, data[key])
  }
  return request({
    url: '/products/updateProductProp/',
    method: 'post',
    data: formData
  })
}

//获取客户的消息规则
export function getRulesApi(data) { 
  const pm={}
  if(data.pro_id){
    pm.pro_id=data.pro_id
  }
  if(data.dev_id){
    pm.dev_id=data.dev_id
  }
  return request({
    url: '/products/getRules/',
    method: 'get',
    params: pm
  })
}

export function searchRulesApi(data) {
  let pm={}
  if(data.name){
    pm.name=data.name
  }
  if(data.target_type){
    pm.target_type=data.target_type
  }
  if(data.status){
    pm.status=data.status
  }
  return request({
    url: '/products/searchRules/',
    method: 'get',
    params: pm
  })
}

//获取产品的消息规则
export function getProductRulesApi(pro_id) {
  return request({
    url: '/products/getRules/',
    method: 'get',
    params: { pro_id: pro_id }
  })
}

//获取设备的消息规则
export function getDevRulesApi(pro_id, dev_id) {
  return request({
    url: '/products/getRules/',
    method: 'get',
    params: {
      pro_id: pro_id,
      dev_id: dev_id
    }
  })
}

export function getRuleDetailApi(rule_id) {
  return request({
    url: '/products/getRuleDetail/',
    method: 'get',
    params: { rule_id: rule_id }
  })
}

//更新规则
export function updateRuleApi(data) {
  //把data对象改为formdata格式  
  const formData = new FormData()
  for (const key in data) {
    formData.append(key, data[key])
  }
  return request({
    url: '/products/updateRule/',
    method: 'post',
    data: formData
  })
}

//删除规则
export function deleteRuleApi(rule_id) {
  const formData = new FormData()
  formData.append('rule_id', rule_id)
  return request({
    url: '/products/deleteRule/',
    method: 'post',
    data: formData
  })
}

//测试规则代码
export function testRuleCodeApi(data) {
  //把data对象改为formdata格式  
  const formData = new FormData()
  for (const key in data) {
    formData.append(key, data[key])
  }
  return request({
    url: '/products/testRuleCode/',
    method: 'post',
    data: formData
  })
}
//获取报警规则
export function getAlarmRulesApi(pro_id) {
  return request({
    url: '/products/getAlarmRules/',
    method: 'get',
    params: { pro_id: pro_id }
  })
}
export function getProductAlarmsApi(pro_id) {
  return request({
    url: '/products/getAlarms/',
    method: 'get',
    params: { pro_id: pro_id }
  })
}

//更新报警状态
export function updateAlarmStatusApi(alarm_id, status) {
  const formData = new FormData()
  formData.append('alarm_id', alarm_id)
  formData.append('status', status)

  return request({
    url: '/products/updateAlarmStatus/',
    method: 'post',
    data: formData
  })
}

export function getDevAlarmsApi(pro_id, dev_id) {
  return request({
    url: '/products/getAlarms/',
    method: 'get',
    params: { pro_id: pro_id, dev_id: dev_id }
  })
}

//获取报警规则详情
export function getAlarmDetailApi(alarm_id) {
  return request({
    url: '/products/getAlarmDetail/',
    method: 'get',
    params: { alarm_id: alarm_id }
  })
}

//更新报警规则
export function updateAlarmApi(data) {
  //把data对象改为formdata格式  
  const formData = new FormData()
  for (const key in data) {
    formData.append(key, data[key])
  }
  return request({
    url: '/products/updateAlarm/',
    method: 'post',
    data: formData
  })
}

//删除报警规则
export function deleteAlarmApi(alarm_id) {
  const formData = new FormData()
  formData.append('alarm_id', alarm_id)
  return request({
    url: '/products/deleteAlarm/',
    method: 'post',
    data: formData
  })
}
