<!-- 产量统计展示页面 -->
<template>
    <div class="container">
        <!-- 返回按钮和标题 -->
        <el-page-header>
            <template #content>
                <span class="text-large font-600 mr-3">产量统计 </span>
            </template>
        </el-page-header>
        <el-divider style="margin: 20px 0px 10px 0px;" />

        <!-- Tab 按钮 -->
        <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="device-tabs">            
            <el-tab-pane label="酒店洗涤量" name="hotel" />
            <el-tab-pane label="设备洗涤量" name="device" />
        </el-tabs>

        <!-- 动态组件 -->
        <component :is="currentComponent" :dev_ids="dev_ids" />

    </div>
</template>

<script setup>
import HotelProduction from './hotelProduction.vue'
import DevProduction from './devProduction.vue'

import { ref, computed } from 'vue';

const activeTab = ref('hotel')
const dev_ids = ref(['WSH25001','WSH25002','WSH25003'])


</script>

<style scoped>
.container {
    padding: 20px;
}
</style>