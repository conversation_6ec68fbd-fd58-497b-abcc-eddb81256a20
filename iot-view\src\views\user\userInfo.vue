<template>
    <div class="container">
        <el-page-header @back="goBack">
            <template #content>
                <span class="text-large font-600 mr-3">账户信息 </span>
                <el-button :icon="Edit" type="primary" @click="editProduct" text />
            </template>
        </el-page-header>

        <!-- 用户基本信息 -->
        <el-descriptions :column="1" size="large" class="compact-descriptions" border>
            <el-descriptions-item label="账户">
                {{ userinfo.username }}
            </el-descriptions-item>
            <el-descriptions-item label="姓名"> {{ userinfo.ch_name }}</el-descriptions-item>
            <el-descriptions-item label="密码">
                <el-button type="primary" @click="changePassword">修改密码</el-button>
            </el-descriptions-item>
            <el-descriptions-item label="公司">{{ userinfo.customer }}</el-descriptions-item>
            <el-descriptions-item label="部门">{{ userinfo.department }}</el-descriptions-item>
            <el-descriptions-item label="电话">{{ userinfo.phone }}</el-descriptions-item>
            <el-descriptions-item label="权限">{{ userinfo.roles }}</el-descriptions-item>

        </el-descriptions>

        <!-- 编辑用户 -->
        <el-dialog v-model="changePasswordDialog" title="修改密码" width="30%">
            <el-form ref="changePasswordFormRef" :model="changePasswordForm" :rules="passwordRules" label-width="120px">
                <el-form-item label="旧密码" prop="password">
                    <el-input v-model="changePasswordForm.password" type="password" />
                </el-form-item>
                <el-form-item label="新密码" prop="new_password">
                    <el-input v-model="changePasswordForm.new_password" type="password" />
                </el-form-item>
                <el-form-item label="确认密码" prop="confirm_password">
                    <el-input v-model="changePasswordForm.confirm_password" type="password" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleChangePassword">修改密码</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getUserInfoApi, changePasswordApi } from '@/api/user';
import { Edit } from '@element-plus/icons-vue';
import {
    Iphone,
    Location,
    OfficeBuilding,
    Tickets,
    User,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus';

const router = useRouter();

const editProductDialog = ref(false);
const editProduct = () => {
    editProductDialog.value = true;
}
const handleEditSuccess = async () => {
    editProductDialog.value = false;
    product.value = await pdStore.requestProduct(pro_id.value);
}

const userinfo = ref({});
const changePasswordDialog = ref(false);
const changePasswordFormRef = ref(null);
const changePasswordForm = ref({
    password: '',
    new_password: '',
    confirm_password: ''
});

//确认密码校验
const validatePassword2 = (rule, value, callback) => {
    if (value === '') {
        callback(new Error('请输入密码'));
    } else if (value !== changePasswordForm.value.new_password) {
        callback(new Error('密码不一致'));
    } else {
        callback();
    }
}
//密码校验
const validatePassword = (rule, value, callback) => {
    if (value === '') {
        callback(new Error('请输入确认密码'));
    } else if (value.length < 6) {
        callback(new Error('密码长度不能小于6位'));
    } else {
        callback();
    }
}
const passwordRules = {
    password: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
    new_password: [{ trigger: 'blur', validator: validatePassword }],
    confirm_password: [{ trigger: 'blur', validator: validatePassword2 }]
};

const changePassword = () => {
    changePasswordDialog.value = true;
}

//提交密码修改
const handleChangePassword = async () => {
    await changePasswordFormRef.value.validate();
    const res = await changePasswordApi(changePasswordForm.value);
    if (res.code === 200) {
        ElMessage.success('密码修改成功');
        changePasswordDialog.value = false;
    }
}

onMounted(async () => {
    //确保组件加载时自动获取device，因为computered只有在依赖发生变化时才会重新计算，
    // 所以需要在组件加载时向后台请求device，再赋值给device
    //返回是一个列表，取第一个
    userinfo.value = await getUserInfoApi();
});

// 返回上一页
const goBack = () => {
    router.back();
};


</script>

<style scoped>
.container {
    padding: 20px;
}

.compact-descriptions {
    width: 40%;
    /* 调整宽度为父容器的50%，你可以根据需要调整这个值 */
    margin: 0 0;
    /* 水平居中 */
    text-align: left;
    margin-top: 30px;
}

.cell-item {
    display: flex;
    align-items: center;
}
</style>