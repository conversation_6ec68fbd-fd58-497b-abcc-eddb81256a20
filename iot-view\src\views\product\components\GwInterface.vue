<template>
    <div>
        <div class="header">
            <el-button type="primary" @click="handleAdd">新增接口</el-button>
            <el-button type="primary" @click="fetchGwBindDevice">刷新</el-button>
        </div>
        <el-table v-loading="loading" :data="gwinterface" style="width: 100%">
            <!-- 接口类型、地址、端口 -->
            <el-table-column prop="dev_name" label="设备名称"></el-table-column>
            <el-table-column prop="dev_id" label="设备ID" width="180"></el-table-column>
            <el-table-column prop="dev_status" label="设备状态" width="180">
                <template #default="{ row }">
                    <el-tag :type="getDevStatusTag(row.dev_status)">{{ getDevStatusLabel(row.dev_status) }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="interface" label="接口" width="180"></el-table-column>
            <el-table-column prop="ip" label="地址" width="180"></el-table-column>
            <el-table-column prop="port" label="端口" width="180"></el-table-column>
            <el-table-column label="操作" width="180" fixed="right">
                <template #default="{ row }">
                    <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
                    <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 编辑弹窗 -->
        <el-dialog v-model="dialogVisible" width="800px" @close="handleClose">
            <GwInterfaceEdit ref="formRef" :gw_id="props.dev_id" @update="handleFormUpdate" @cancel="handleClose" />
        </el-dialog>
    </div>
</template>

<!-- 获取网关关联的设备 -->

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getGwInterfaceApi, deleteInterfaceApi} from '@/api/products';
import GwInterfaceEdit from './GwInterfaceEdit.vue';
import { useDataStore } from '@/stores/modules/data';
const dataStore = useDataStore();

const props = defineProps({
    dev_id: {
        type: String,
        required: true
    }
});

const gwinterface = ref([]);
const loading = ref(false);
const dialogVisible = ref(false);

const formRef = ref(null);

// 表单数据
const formData = reactive({
    dev_name: '',
    dev_id: '',
    interface: 'TCP',
    ip: '',
    port: 502,
    baudrate: '9600',
    databits: '8',
    parity: 'NONE',
    stopbits: '1'
});

// 获取网关接口信息
const fetchGwInterface = async () => {
    loading.value = true;
    try {
        gwinterface.value = await getGwInterfaceApi(props.dev_id);
        console.log('获取网关接口信息', gwinterface.value);
    } catch (error) {
        ElMessage.error('获取接口信息失败');
    } finally {
        loading.value = false;
    }
};

//设备状态标签
const getDevStatusLabel = (status) => {
    const map = {
        'RUN': '运行',
        'STANDBY': '待机',
        'FAULT': '故障',
        'OFFLINE': '离线'
    }
    return map[status] || status
}

//设备状态标签样式
const getDevStatusTag = (status) => {
    const map = {
        'RUN': 'success',
        'STANDBY': 'warning',
        'FAULT': 'danger',
        'OFFLINE': 'info'
    }
    return map[status] || status
}

// 新增接口
const handleAdd = () => {    
    dialogVisible.value = true;
    formRef.value?.open({
        isEditing: false,
        interface_id: '',
        data: {}
    })
}

// 编辑接口
const handleEdit = (row) => {   
    
    dialogVisible.value = true;
    formRef.value?.open({
        isEditing: true,
        interface_id: row.id,
        data: row
    })
}

// 删除接口
const handleDelete = async (row) => {
    try {
        await ElMessageBox.confirm('确定要删除该接口吗？', '提示', {
            type: 'warning'
        });
        await deleteInterfaceApi(row.id);
        ElMessage.success('删除成功');
        fetchGwInterface();
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('删除失败');
        }
    }
}

// 处理表单数据更新
const handleFormUpdate = (newData) => {
    //重新获取，关闭弹窗
    fetchGwInterface();
    dialogVisible.value = false;
}

// 关闭弹窗
const handleClose = () => {
    dialogVisible.value = false;      
}

// 刷新列表
const fetchGwBindDevice = async () => {
    await fetchGwInterface();    
}
watch(gwinterface, (newVal) => {
    console.log('更新网关接口数据', newVal);
    if (newVal && newVal.length > 0) {  
        
        //向后台查询设备状态        
        dataStore.requestDevsState(newVal.map(item => item.dev_id));
        //更新设备状态
        newVal.forEach(item => {
            item.dev_status = dataStore.devState[item.dev_id] || "OFFLINE";            
        }); 
        console.log('更新后的设备状态', newVal);       
        //更新表格数据
        gwinterface.value = newVal;
    }
}, { immediate: true }, { deep: true });

onMounted(async () => {
    await fetchGwInterface();    
});

</script>

<style scoped>
.header {
    margin-bottom: 10px;
    display: flex;
    gap: 10px;
}
</style>