<template>
    <div>
        <el-result icon="success" title="已成功登出" sub-title="请重新登录" />
    </div>
</template>

<script setup>
import { onMounted} from 'vue'
import { useAccount } from '@/stores/modules/account'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
const userStore = useAccount()
const router = useRouter()
onMounted(() => {
    userStore.logout()
    ElMessage.success('已成功登出')
    router.push('/')
})

</script>