<template>
    <div class="code-editor">
        <!-- 帮助按钮和帮助文档 -->
        <div class="help-section">
            <el-button type="info" link @click="toggleHelper">
                <el-icon>
                    <QuestionFilled />
                </el-icon>
                帮助文档
            </el-button>
            <div class="helper-text" v-show="showHelper">
                <pre class="code-example">
                    #示例代码
                    # 单位转换和数据清洗
                    if 'voltage' in data:
                    voltage = data['voltage']

                    # 电压单位转换 (mV to V)
                    result['voltage'] = voltage / 1000

                    # 添加功率计算
                    if 'current' in data:
                    current = data['current']
                    result['power'] = (voltage / 1000) * current

                    # 数据有效性检查
                if not (0 &lt;= voltage &lt;= 24000): # 24V系统
                result['valid'] = False # 数据
                event={ # 事件
                'type': 'alert',
                'name': '电压值异常',
                'value': 'voltage={}mV'.format(voltage),
                'level': 'error',
                'device_id': data['device_id'],
                'timestamp': data['timestamp']

                }
                dev_status = 'ALARM' # 设备状态
                </pre>
            </div>
        </div>

        <!-- 主编辑器 -->
        <div ref="editorRef" class="editor-container"></div>

        <!-- 测试代码部分 -->
        <div class="test-section">
            <el-button type="primary" @click="toggleTest">
                <el-icon>
                    <Monitor />
                </el-icon>
                {{ showTest ? '收起测试' : '测试代码' }}
            </el-button>
        </div>

        <!-- 测试面板 -->
        <el-collapse-transition>
            <div v-show="showTest" class="test-panel">
                <div class="test-editors">
                    <div class="input-editor">
                        <div class="editor-title">测试参数</div>
                        <div ref="inputEditorRef" class="test-editor-container"></div>
                    </div>
                    <div class="output-editor">
                        <div class="editor-title">运行结果</div>
                        <div ref="outputEditorRef" class="test-editor-container"></div>
                    </div>
                </div>
                <div class="test-actions">
                    <el-button type="primary" @click="runTest" :loading="testing">
                        <el-icon>
                            <VideoPlay />
                        </el-icon>
                        运行测试
                    </el-button>
                </div>
            </div>
        </el-collapse-transition>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { testRuleCodeApi } from '@/api/products'
import { ElMessage } from 'element-plus'
import { QuestionFilled, Monitor, VideoPlay } from '@element-plus/icons-vue'

const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue'])

const editorRef = ref(null)
const inputEditorRef = ref(null)
const outputEditorRef = ref(null)
let editor = null
let inputEditor = null
let outputEditor = null

const showHelper = ref(false)
const showTest = ref(false)
const testing = ref(false)

const toggleTest = () => {
    showTest.value = !showTest.value
}
const toggleHelper = () => {
    showHelper.value = !showHelper.value
}

const runTest = async () => {
    if (!editor || !inputEditor || !outputEditor) return
    
    const code = editor.state.doc.toString()
    const input = inputEditor.state.doc.toString()    
    const res=await testRuleCodeApi({'code':code,'test_data':input})
    //如果res.code为500，则显示错误信息

    outputEditor.dispatch({
        changes: {
            from: 0,
            to: outputEditor.state.doc.length,
            insert: JSON.stringify(res.data, null, 2)
        }
    })
}
const defaultCode = `
    """
    对设备上报数据进行预处理
    :data: 设备上报的数据字典
    :result: 处理后的数据字典
    :event: 要触发的事件字典
    :dev_status: 设备状态
    """
    # TODO: 添加数据处理逻辑,不用return
`

const cancelCode = () => {
    console.log('cancelCode')
    emit('update:modelValue', '')
}

const confirmCode = () => {
    console.log('confirmCode')
    emit('update:modelValue', getCode())
}

const defaultTestInput = `{    
    "timestamp": 1648432611000,
    "voltage": 12000,
    "current": 1.5,
    "temperature": 25
}`

const defaultTestOutput = `{
    "result": {
        "voltage": 12.0,
        "current": 1.5,
        "temperature": 25
    }
}`

onMounted(async () => {
    try {
        const [
            { EditorView, basicSetup },
            { python },            
            { oneDark }
        ] = await Promise.all([
            import('codemirror'),
            import('@codemirror/lang-python'),            
            import('@codemirror/theme-one-dark')
        ])

        // 主编辑器
        editor = new EditorView({
            doc: props.modelValue || defaultCode,
            extensions: [
                basicSetup,
                python(),
                oneDark,
                EditorView.updateListener.of(update => {
                    if (update.docChanged) {
                        emit('update:modelValue', update.state.doc.toString())
                    }
                })
            ],
            parent: editorRef.value
        })

        // 输入编辑器
        inputEditor = new EditorView({
            doc: defaultTestInput,
            extensions: [
                basicSetup,
                python(),
                oneDark
            ],
            parent: inputEditorRef.value
        })

        // 输出编辑器
        outputEditor = new EditorView({
            doc: '',
            extensions: [
                basicSetup,
                python(),
                oneDark,
                EditorView.editable.of(false)
            ],
            parent: outputEditorRef.value
        })
    } catch (error) {
        console.error('Failed to load CodeMirror:', error)
    }
})

onBeforeUnmount(() => {
    if (editor) editor.destroy()
    if (inputEditor) inputEditor.destroy()
    if (outputEditor) outputEditor.destroy()
})

// 对外暴露获取代码方法
const getCode = () => {
    return editor ? editor.state.doc.toString() : ''
}

defineExpose({
    getCode
})
</script>

<style scoped>
.code-editor {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.help-section {
    padding: 10px;
    border-bottom: 1px solid #dcdfe6;
}

.helper-text {
    margin-top: 10px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

.editor-container {
    height: 300px;
    overflow: auto;
}

.test-section {
    padding: 10px;
    border-top: 1px solid #dcdfe6;
    display: flex;
    justify-content: flex-end;
}

.test-panel {
    padding: 20px;
    background-color: #f5f7fa;
    border-top: 1px solid #dcdfe6;
}

.test-editors {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.input-editor,
.output-editor {
    background-color: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
}

.editor-title {
    padding: 8px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #dcdfe6;
    font-weight: bold;
}

.test-editor-container {
    height: 200px;
    overflow: auto;
}

.test-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.code-example {
    font-family: Consolas, Monaco, 'Courier New', monospace;
    font-size: 14px;
    white-space: pre-wrap;
    margin: 0;
}

:deep(.cm-editor) {
    height: 100%;
}

:deep(.cm-scroller) {
    font-family: Consolas, Monaco, 'Courier New', monospace;
    font-size: 14px;
}
</style>
