<template>
    <!-- 反查布草洗涤工艺 -->
    <div class="history-container">
        <!-- 标题栏 -->
        <div class="header">
            布草洗涤工艺
        </div>
        
        <!-- 时间选择和操作按钮 -->
        <div class="toolbar">
            <el-date-picker v-model="timeValue" type="date" value-format="YYYY-MM-DD" @change="changeTime" />            
            <div class="buttons">
                <!-- 查询 -->
                <el-button type="primary" :icon="TrendCharts" @click="fetchData">查询</el-button>
                <!-- 导出 -->
                <el-button type="success" :icon="Download" @click="exportData">导出</el-button>
            </div>
        </div>

        <!-- 数据表格 -->
        <el-table ref="tableRef" :data="tableData" style="height:calc(100vh - 200px)" border v-loading="loading">
            <!-- 开始时间 -->
            <el-table-column prop="start_time" label="开始时间" width="180" fixed="left" :sortable="true" />
            <!-- 结束时间 -->
            <el-table-column prop="end_time" label="结束时间" width="180" fixed="left" />
            <!-- 布草 -->
            <el-table-column prop="tex_name" label="布草" width="150" fixed="left" :sortable="true" />
            <!-- 设备 -->
            <el-table-column prop="dev_name" label="设备" width="120" fixed="left" :sortable="true" />
            <!-- 程序号 -->
            <el-table-column prop="program_id" label="程序号" width="100" fixed="left" :sortable="true" />
            <!-- 产量 -->
            <el-table-column prop="production" label="产量" width="80" fixed="left" />
            <!-- 酒店 -->
            <el-table-column prop="hotel_name" label="酒店" width="120" fixed="left" :sortable="true" />            
            <!-- 程序时间，根据结束时间减去开始时间来计算 -->
            <el-table-column prop="duration" label="程序时间" width="120" fixed="left" :sortable="true" />            
            <el-table-column label="历史数据">
                <template #default="scope">
                    <el-button type="primary" @click="viewDetailChart(scope.row)">查看数据曲线</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 数据曲线 -->
        <el-dialog v-model="historyChartDialog" title="洗涤工艺图表" width="50%" height="50%">
            <HistoryChart :data="chartData" :chart_series="chartSeries" />
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, TrendCharts } from '@element-plus/icons-vue'
import { getDevsProcessApi,getDevsProcessDetailApi } from '@/api/aggregate'
import {getTextilesApi} from '@/api/textile'
import { useProductsStore } from '@/stores/modules/products'
import { useDataStore } from '@/stores/modules/data'
import HistoryChart from '@/views/chart/historyChart.vue'



const props = defineProps({
    hotel_id: {
        type: String,        
        required: true
    }
})
const pdStore = useProductsStore()

const tableRef = ref(null)
const timeValue = ref('')
const tableData = ref([])
const loading = ref(false)
const historyChartDialog = ref(false)
const chartData = ref([])
const chartSeries = ref({})


// 查看数据曲线
const viewDetailChart =async (row) => {    
    //获取该程序的详情数据    
    const prDetail=await getDevsProcessDetailApi({process_ids:[row.process_id]})
    chartData.value=prDetail.map(item=>{
        item.data['create_time']=item.create_time
        item.data['dev_id']=item.dev_id
       return item.data 
    })
    //根据属性生成图表系列
    //获取设备属性的名字
    const dev_id=row.dev_id
    const dev_nodes=await pdStore.getDevProps(dev_id)
    let props={}    
    chartData.value.forEach(item=>{
        //找出值为数字格式的属性
        Object.keys(item).forEach(key=>{
            if(typeof item[key]==='number'){
                let node_name=dev_nodes.find(node=>node.identifier===key)?.name||key
                props[key]={identifier:key,name:node_name,type:'line'}
            }
        })
    })

    chartSeries.value=Object.values(props)    
    // 弹出对话框
    historyChartDialog.value = true
}

const textiles=ref([])
// 获取纺织品列表
const fetchTexData = async () => {
    loading.value = true
    let d = {}
    if (props.hotel_id) {
        d.hotel_ids = [props.hotel_id]
    }

    try {
        textiles.value = await getTextilesApi(d)    
        return textiles.value    
    } catch (error) {
        ElMessage.error('获取布草列表失败')
    } finally {
        loading.value = false
    }
}

// 获取洗涤工艺数据
const fetchProcessData = async () => {
    if (!timeValue.value) {
        ElMessage.warning('请选择时间')
        return
    }

    loading.value = true
    try {
        const startTime = timeValue.value + ' 00:00:00'
        const endTime = timeValue.value + ' 23:59:59'
        const params = {
            tex_ids: textiles.value.map(item=>item.tex_id),
            //时间转字符串格式化
            start_time: startTime,
            end_time: endTime,
        }
        const res = await getDevsProcessApi(params)
        res.forEach(item=>{   
            let start_time=new Date(item.start_time)
            let end_time=new Date(item.end_time)
            
            //计算程序时间
            item.duration=Math.floor((end_time.getTime()-start_time.getTime())/(1000*60))
        })
        return res||[]
    } catch (error) {
        ElMessage.error('获取历史数据失败')
        console.error('获取历史数据失败:', error)
    } finally {
        loading.value = false
    }
}


const fetchData=async ()=>{
    const texs=await fetchTexData()
    console.log('texs',texs)
    const processData=await fetchProcessData()
    console.log('processData',processData)
    //获取dev_ids
    const dev_ids=processData.map(item=>item.dev_id)
    const devs=await pdStore.getDevsInfo(dev_ids)
    console.log('devs',devs)
    //把布草和酒店信息添加到res中
    processData.forEach(item=>{
        item.tex_name=texs.find(tex=>tex.tex_id===item.tex_id)?.name
        item.hotel_name=texs.find(tex=>tex.tex_id===item.tex_id)?.hotel_name
        item.dev_name=devs.find(dev=>dev.dev_id===item.dev_id)?.name
    })
    //重置排序
    tableRef.value.clearSort()
    tableData.value=processData
}

const filterData = (value, row, column) => {
    // 筛选数据
    const property = column['property']
    return row[property] === value
}

// 导出数据
const exportData = async () => {
    if (!historyData.value.length) {
        ElMessage.warning('暂无数据可导出')
        return
    }

    try {
        // 准备导出数据
        const exportData = historyData.value.map(item => {
            const row = {
                '日期': item.create_time,
                '酒店名': item.demand,
                '洗涤量': item.production,
                '详情': item.detail
            }
            return row
        })

        // 创建工作簿
        const wb = XLSX.utils.book_new()
        const ws = XLSX.utils.json_to_sheet(exportData)
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

        // 导出文件
        const fileName = `酒店洗涤量统计_${formatTime(new Date())}.xlsx`
        XLSX.writeFile(wb, fileName)
    } catch (error) {
        ElMessage.error('导出失败')
        console.error('导出失败:', error)
    }
}

// 格式化时间
const formatTime = (time) => {
    return new Date(time).toLocaleString()
}

// 初始化
onMounted(async () => {
    const startTime = formatDate(new Date())
    timeValue.value = startTime
})

//转格式化时间,YYYY-MM-DD HH:mm:ss
const formatDate = (date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    const second = date.getSeconds().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}

</script>

<style scoped>
.history-container {
    padding-top: 10px;
    height: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;
    /* 防止工具栏被压缩 */
}

.buttons {
    padding-left: 20px;
    display: flex;
    gap: 10px;
}


/* 固定表头 */
:deep(.el-table__header-wrapper) {
    position: sticky;
    top: 0;
    z-index: 1;
}

.pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
    /* 防止分页被压缩 */
}
</style>