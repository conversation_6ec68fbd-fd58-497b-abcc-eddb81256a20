<template>
    <!-- 布草交接新建或编辑表单 -->
    <div class="form-header">{{ title }}</div>
    <div class="property-form">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" size="default">
            <el-form-item label="布草ID" prop="tex_id">
                <el-autocomplete v-model="formData.tex_id" :fetch-suggestions="querySearch" clearable
                    class="inline-input w-50" placeholder="请输入布草ID" @select="handleSelect">
                    <template #default="{ item }">
                        {{ item.value }}-{{ item.name }}                        
                    </template>
                </el-autocomplete>
            </el-form-item>
            <el-form-item label="名 称">
                <div>{{ tex_name }}</div>
            </el-form-item>
            <el-form-item label="数 量" prop="quantity">
                <el-input-number v-model="formData.quantity" :min="1" :max="1000" />
            </el-form-item>
            <el-form-item label="时 间" prop="create_time">
                <!-- 默认当前时间 -->
                <el-date-picker v-model="formData.create_time" type="datetime" placeholder="请选择时间"
                    value-format="YYYY-MM-DD HH:mm:ss" />
            </el-form-item>
            <el-form-item label="备 注" prop="remark"></el-form-item>
        </el-form>
        <div class="footer">
            <el-button class="submit-btn" type="primary" @click="submitForm">{{ isEdit ? '更新' : '新建' }}</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, defineEmits, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useTextileStore } from '@/stores/modules/textile';
import { getTextilesApi, createTexRecordApi, updateTexRecordApi } from '@/api/textile'


const props = defineProps({
    type: {
        type: String,
        required: true
    },
    record: {
        type: Object
    }
})

const texStore = useTextileStore()
const emit = defineEmits(['success'])

const formRef = ref(null)
const isEdit = ref(false)

const title = computed(() => {
    const typeMap = {
        IN: '新增收件',
        OUT: '新增送货',
        PAY: '新增赔偿'
    }
    if (props.record) {
        return "编辑"
    }
    return typeMap[props.type]
})
// 表单数据
const formData = ref({
    tex_id: '',
    type: props.type,
    quantity: 0,
    remark: '',
    create_time: new Date().toISOString()
})

// 表单验证规则
const rules = {
    name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
    pro_id: [{ required: true, message: '请选择产品', trigger: 'change' }],
}

const textiles = ref([])

const querySearch = (queryString, cb) => {
    const results = queryString
        ? textiles.value.filter(createFilter(queryString))
        : textiles.value
    // call callback function to return suggestions
    cb(results)
}
const createFilter = (queryString) => {
    return (tex) => {
        return (
            tex.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
        )
    }
}
//建议被选中
const handleSelect = (item) => {
    console.log("建议被选中", item)
    tex_id.value = item.value
}

const hotel_name = computed(() => {
    return textiles.value.find(item => item.value == formData.value.tex_id)?.hotel_name
})
const tex_name = computed(() => {
    let tex_name=textiles.value.find(item => item.value == formData.value.tex_id)?.name
    let hotel_name=textiles.value.find(item => item.value == formData.value.tex_id)?.hotel_name
    if(tex_name&&hotel_name){
        return tex_name + " <" + hotel_name+">"
    }
    return ''
})

watch(() => props.record, async (newVal) => {
    // 重置表单
    formData.value = {
        tex_id: '',
        type: props.type,
        quantity: 0,
        remark: '',
        create_time: new Date().toLocaleString()
    }
    isEdit.value = false
    if (newVal) {
        isEdit.value = true
        formData.value = { ...newVal }
    }
}, { immediate: true })


// 提交表单
const submitForm = async () => {
    if (!formRef.value) return

    try {
        await formRef.value.validate()
        formData.value.hotel_id = texStore.textiles.find(item => item.tex_id == formData.value.tex_id)?.hotel_id

        if (isEdit.value) {
            await updateTexRecordApi(formData.value)
            ElMessage.success('更新成功')
        } else {
            await createTexRecordApi(formData.value)
            ElMessage.success('新建成功')
        }
        // 通知父组件刷新列表        
        formRef.value.resetFields()
        emit('success')
    } catch (error) {
        ElMessage.error(isEdit.value ? '更新失败' : '添加失败')
        console.error('操作失败:', error)
    }
}

onMounted(async () => {
    //获取用户全部纺织品
    let res = await texStore.requestTextiles()
    textiles.value = res.map(item => {
        return {
            value: item.tex_id,
            name: item.name,
            hotel_name: item.hotel_name
        }
    })
})

</script>
<style scoped>
.property-form {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
}

.form-header {
    position: absolute;
    top: 0;
    left: 25px;
    width: 100%;
    height: 50px;
    line-height: 50px;
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 40px;
}

.el-form-item {
    .el-input {
        width: 300px;
        height: 40px;
    }

    .el-select {
        width: 300px;
        height: 40px;
    }
}

.submit-btn {
    margin-top: 20px;
    width: 80%;
    margin-left: 10%;
}

.rule-form {
    max-width: 600px;
}
</style>