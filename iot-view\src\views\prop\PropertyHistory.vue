<template>
    <!-- 属性历史数据表格 -->
    <div class="history-container">
        <!-- 时间选择和操作按钮 -->
        <div class="toolbar">
            <el-date-picker style="max-width: 700px;" v-model="timeRange" type="datetimerange" range-separator="至" start-placeholder="开始时间"
                end-placeholder="结束时间" :default-time="defaultTime" value-format="YYYY-MM-DD HH:mm:ss" />
            <div class="buttons">
                <el-button type="primary" :icon="Search" @click="fetchHistoryData">查询</el-button>
                <!-- 查看数据曲线 -->
                <el-button type="primary" :icon="TrendCharts" @click="showHistoryChart">查看曲线</el-button>
                <el-button type="success" :icon="Download" @click="exportData">导出</el-button>
            </div>
        </div>

        <!-- 数据表格 -->
        <el-table :data="historyData" style="width: 100%" height="calc(100vh - 200px)" border v-loading="loading">
            <el-table-column prop="create_time" label="时间" width="180" fixed="left" />
            <el-table-column v-for="prop in deviceProps" :key="prop.identifier" :prop="'values.' + prop.identifier"
                :label="prop.name+' '+prop.unit" align="center">
                <template #default="{ row }">
                    {{ row[prop.identifier] }}
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[20, 50, 100]"
                layout="total, sizes, prev, pager, next" :total="totalCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>
        <!-- 数据曲线 -->
        <el-dialog v-model="historyChart" title="数据曲线" width="50%" height="50%">
            <HistoryChart :data="chartData" :chart_series="chartSeries" :chart_type="chart_type" />
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Download, TrendCharts } from '@element-plus/icons-vue'
import { getDevProcessDetailHistoryApi } from '@/api/data'
import { useProductsStore } from '@/stores/modules/products'
import HistoryChart from '@/views/chart/historyChart.vue'

const props = defineProps({
    dev_id: {
        type: String,
        required: true
    },
    pro_id: {
        type: String,
        required: true
    }
})

const pdStore = useProductsStore()
const deviceProps = ref([])
const timeRange = ref([])
const historyData = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)

const totalCount = ref(0)
const defaultTime = [
    new Date(2025, 1, 1, 0, 0, 0),
    new Date(2025, 1, 1, 23, 59, 59)
]

// 获取设备属性列表
const fetchDeviceProps = async () => {
    try {
        const dev_props = await pdStore.requestProductProps(props.pro_id)
        //过滤出type为PROP的设备属性赋值给deviceProps
        deviceProps.value = dev_props.filter(prop => prop.type === 'PROP') || []
        console.log('设备属性列表：', deviceProps.value)
    } catch (error) {
        ElMessage.error('获取设备属性失败')
        console.error('获取设备属性失败:', error)
    }
}

// 获取历史数据
const fetchHistoryData = async () => {
    if (!timeRange.value || !timeRange.value[0] || !timeRange.value[1]) {
        ElMessage.warning('请选择时间范围')
        return
    }

    if (!deviceProps.value.length) {
        ElMessage.warning('没有可查询的属性')
        return
    }

    loading.value = true
    try {
        const [startTime, endTime] = timeRange.value
        console.log('startTime:', startTime)
        console.log('endTime:', endTime)
        const params = {
            dev_id: props.dev_id,
            //时间转字符串格式化
            start_time: startTime,
            end_time: endTime,
            page: currentPage.value,
            page_size: pageSize.value
        }

        const { data, total } = await getDevProcessDetailHistoryApi(params)
        // 重置历史数据
        historyData.value = []

        // 处理数据，直接使用data对象，添加时间戳
        data.forEach(item => {
            const rowData = {
                create_time: item.create_time,
                ...item.data,
                tex_id: item.tex_id,
                total_production: item.total_production,
                production: item.production
            }
            historyData.value.push(rowData)
        })
        // 更新总数据
        totalCount.value = total || data.length
    } catch (error) {
        ElMessage.error('获取历史数据失败')
        console.error('获取历史数据失败:', error)
    } finally {
        loading.value = false
    }
}


// 更换页面大小
const handleSizeChange = (val) => {
    pageSize.value = val
    fetchHistoryData()
}

// 更换当前页
const handleCurrentChange = (val) => {
    currentPage.value = val
    fetchHistoryData()
}

//生成图表曲线数据
const getChartData = () => {
    const chartData = []
    //只保留值是数字格式的数据
    historyData.value.map(item => {
        const row = {
            create_time: item.create_time
        }
        // 添加每个属性的值
        Object.keys(item).forEach(key => {
            if (typeof item[key] === 'number') {
                row[key] = item[key]
            }
        })
        chartData.push(row)
    })
    return chartData
}

const getChartSeries = () => {
    const series = []
    deviceProps.value.filter(item => item.data_type == 'NUMBER').forEach(prop => {
        series.push({
            name: prop.name,
            identifier: prop.identifier,
            type: 'line'
        })
    })
    return series
}

// 查看数据曲线
const historyChart = ref(false)
const chart_type = ref('line')
const chartData = ref([])
const chartSeries = ref([])
const showHistoryChart = () => {
    // 获取图表数据
    chartData.value = getChartData()
    //获取图表系列
    chartSeries.value = getChartSeries()
    // 弹出对话框
    historyChart.value = true

}

// 导出数据
const exportData = async () => {
    if (!historyData.value.length) {
        ElMessage.warning('暂无数据可导出')
        return
    }

    try {
        // 准备导出数据
        const exportData = historyData.value.map(item => {
            const row = {
                '时间': item.create_time
            }
            // 添加每个属性的值
            deviceProps.value.forEach(prop => {
                row[prop.name] = item[prop.identifier]
            })
            return row
        })

        // 创建工作簿
        const wb = XLSX.utils.book_new()
        const ws = XLSX.utils.json_to_sheet(exportData)
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

        // 导出文件
        const fileName = `设备历史数据_${formatTime(new Date())}.xlsx`
        XLSX.writeFile(wb, fileName)
    } catch (error) {
        ElMessage.error('导出失败')
        console.error('导出失败:', error)
    }
}

// 格式化时间
const formatTime = (time) => {
    return new Date(time).toLocaleString()
}



// 初始化
onMounted(async () => {
    // 设置默认时间范围为最近24小时
    const end = new Date()
    const start = new Date()
    start.setDate(start.getDate() - 1)

    const endTime = formatDate(end)
    const startTime = formatDate(start)

    timeRange.value = [startTime, endTime]

    // 获取设备属性列表
    await fetchDeviceProps()
    // 获取历史数据
    if (deviceProps.value.length > 0) {
        await fetchHistoryData()
    }
})

//转格式化时间,YYYY-MM-DD HH:mm:ss
const formatDate = (date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    const second = date.getSeconds().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}
</script>

<style scoped>
.history-container {
    padding-top: 10px;
    height: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: ;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;
    /* 防止工具栏被压缩 */
}

.buttons {
    padding-left: 20px;
    display: flex;
    gap: 20px;
}


/* 固定表头 */
:deep(.el-table__header-wrapper) {
    position: sticky;
    top: 0;
    z-index: 1;
}

.pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
    /* 防止分页被压缩 */
}
</style>