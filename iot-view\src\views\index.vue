<template>
  <main class="home-container">
    <!-- 顶部横幅 -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">智能物联网监控平台</h1>
        <p class="hero-subtitle">实时监控 · 智能分析 · 高效管理</p>
        <div class="hero-stats">
          <div class="stat-item">
            <div class="stat-number">7</div>
            <div class="stat-label">设备总数</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">3</div>
            <div class="stat-label">设备类型</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">24/7</div>
            <div class="stat-label">实时监控</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 功能介绍 -->
    <section class="features-section">
      <div class="container">
        <h2 class="section-title">平台核心功能</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="icon-monitor"></i>
            </div>
            <h3>实时监控</h3>
            <p>24小时不间断监控设备运行状态，实时获取设备数据，确保生产安全稳定</p>
            <ul class="feature-list">
              <li>设备状态实时显示</li>
              <li>数据自动采集</li>
              <li>异常状态预警</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="icon-chart"></i>
            </div>
            <h3>数据分析</h3>
            <p>强大的数据分析能力，多维度图表展示，帮助优化生产流程和设备管理</p>
            <ul class="feature-list">
              <li>产量趋势分析</li>
              <li>设备效率统计</li>
              <li>能耗数据分析</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="icon-alert"></i>
            </div>
            <h3>智能报警</h3>
            <p>智能报警系统，及时发现设备异常，支持多种通知方式，降低故障风险</p>
            <ul class="feature-list">
              <li>实时报警推送</li>
              <li>多级报警机制</li>
              <li>历史报警记录</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="icon-screen"></i>
            </div>
            <h3>大屏展示</h3>
            <p>专业的大屏展示界面，适合会议室和监控中心，全面展示关键指标</p>
            <ul class="feature-list">
              <li>设备分布图</li>
              <li>实时数据看板</li>
              <li>生产统计图表</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="icon-device"></i>
            </div>
            <h3>设备管理</h3>
            <p>完善的设备管理体系，支持设备档案、维护记录、性能评估等功能</p>
            <ul class="feature-list">
              <li>设备档案管理</li>
              <li>维护计划制定</li>
              <li>性能评估报告</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="icon-energy"></i>
            </div>
            <h3>能耗管理</h3>
            <p>精确的能耗监控和分析，帮助企业优化能源使用，降低运营成本</p>
            <ul class="feature-list">
              <li>实时能耗监控</li>
              <li>能耗趋势分析</li>
              <li>节能建议推荐</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- 设备类型介绍 -->
    <section class="devices-section">
      <div class="container">
        <h2 class="section-title">监控设备类型</h2>
        <div class="devices-grid">
          <div class="device-card">
            <div class="device-image">
              <img src="/images/washer.png" alt="水洗机" />
            </div>
            <h3>水洗机设备</h3>
            <p>监控水洗机运行状态、水温、转速等关键参数，确保洗涤质量</p>
            <div class="device-count">当前设备：3台</div>
          </div>

          <div class="device-card">
            <div class="device-image">
              <img src="/images/dryer.png" alt="烘干机" />
            </div>
            <h3>烘干机设备</h3>
            <p>实时监控烘干温度、湿度、运行时间，保证烘干效果</p>
            <div class="device-count">当前设备：2台</div>
          </div>

          <div class="device-card">
            <div class="device-image">
              <img src="/images/qin.png" alt="后整理设备" />
            </div>
            <h3>后整理设备</h3>
            <p>监控后整理设备的压力、温度、速度等参数，确保产品质量</p>
            <div class="device-count">当前设备：2台</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 平台优势 -->
    <section class="advantages-section">
      <div class="container">
        <h2 class="section-title">平台优势</h2>
        <div class="advantages-grid">
          <div class="advantage-item">
            <div class="advantage-number">01</div>
            <h3>高可靠性</h3>
            <p>采用先进的物联网技术，确保数据传输稳定可靠，系统运行稳定</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-number">02</div>
            <h3>易于部署</h3>
            <p>模块化设计，支持快速部署和扩展，降低实施成本和周期</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-number">03</div>
            <h3>智能化</h3>
            <p>集成AI算法，提供智能分析和预测功能，提升管理效率</p>
          </div>
          <div class="advantage-item">
            <div class="advantage-number">04</div>
            <h3>可扩展</h3>
            <p>支持多种设备接入，可根据业务需求灵活扩展监控范围</p>
          </div>
        </div>
      </div>
    </section>
  </main>
</template>

<script setup>
import { ref, onMounted } from 'vue'

onMounted(() => {
  // 添加页面加载动画
  const cards = document.querySelectorAll('.feature-card, .device-card')
  cards.forEach((card, index) => {
    setTimeout(() => {
      card.style.opacity = '1'
      card.style.transform = 'translateY(0)'
    }, index * 100)
  })
})
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-section {
  height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4));
  background-size: cover;
  background-position: center;
  color: white;
  text-align: center;
}

.hero-content {
  max-width: 800px;
  padding: 0 20px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 3rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 3rem;
  opacity: 0.9;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 4rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  color: #64b5f6;
}

.stat-label {
  font-size: 1rem;
  opacity: 0.8;
  margin-top: 0.5rem;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 3rem;
  color: white;
}

.features-section {
  padding: 5rem 0;
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  color: white;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(30px);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(45deg, #64b5f6, #42a5f5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
}

.feature-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.feature-card p {
  margin-bottom: 1.5rem;
  opacity: 0.9;
  line-height: 1.6;
}

.feature-list {
  list-style: none;
  padding: 0;
  text-align: left;
}

.feature-list li {
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.feature-list li:before {
  content: "✓";
  color: #64b5f6;
  margin-right: 0.5rem;
}

.devices-section {
  padding: 5rem 0;
  background: rgba(0,0,0,0.1);
}

.devices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.device-card {
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  color: white;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(30px);
}

.device-image {
  width: 120px;
  height: 120px;
  margin: 0 auto 1.5rem;
  background: rgba(255,255,255,0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.device-image img {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.device-count {
  background: linear-gradient(45deg, #64b5f6, #42a5f5);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  margin-top: 1rem;
  display: inline-block;
}

.advantages-section {
  padding: 5rem 0;
  background: rgba(255,255,255,0.05);
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.advantage-item {
  text-align: center;
  color: white;
  padding: 2rem;
}

.advantage-number {
  font-size: 3rem;
  font-weight: 700;
  color: #64b5f6;
  margin-bottom: 1rem;
}

.advantage-item h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.advantage-item p {
  opacity: 0.9;
  line-height: 1.6;
}

/* 图标样式 */
.icon-monitor:before { content: "📊"; }
.icon-chart:before { content: "📈"; }
.icon-alert:before { content: "🚨"; }
.icon-screen:before { content: "🖥️"; }
.icon-device:before { content: "⚙️"; }
.icon-energy:before { content: "⚡"; }

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-stats {
    flex-direction: column;
    gap: 2rem;
  }
  
  .features-grid,
  .devices-grid {
    grid-template-columns: 1fr;
  }
}
</style>
