<template>
    <!-- 增加表格标题 -->
     <div class="table-header">
        <div class="table-title">实时数据</div>
        <div class="data-time">{{update_time}}</div>
    </div>
    
    <!-- 使用 Element Plus 的表格组件 -->
    <el-table :data="tableData" :show-header="false" size="small" border>
        <el-table-column prop="name" label="参数" align="center">
            <template #default="scope">
            <span class="prop-name">{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="value" label="数据" align="center">
            <template #default="scope">
            <span class="prop-value">{{ scope.row.value }}</span>
            <span class="prop-unit">{{ scope.row.unit }}</span>
          </template>
        </el-table-column>
    </el-table>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useDataStore } from '@/stores/modules/data'
import { useProductsStore } from '@/stores/modules/products'

const props = defineProps({
    dev_id: {
        type: String,
        required: true
    }
})

const dataStore = useDataStore()
const pdStore = useProductsStore()

const tableData = ref([])
const chartProps = ref([])    //设备属性
const update_time=ref('') //更新时间

// 获取设备属性定义
const getDeviceChartProps = async (dev_id) => {
    if (!dev_id) return

    // 先从 store 中获取
    let dev_props = await pdStore.getDevProps(dev_id)
    if (!dev_props || !dev_props.length) {
        console.warn(`设备 ${dev_id} 没有属性定义`)
        return []
    }
    // 转换属性定义格式,只保留数值类型的属性
    const _props = dev_props?.map(prop => ({
            identifier: prop.identifier,
            name: prop.name,
            unit: prop.unit || ''
        })) || []

    console.log('图表属性', _props)
    return _props
}

//更新表格数据
const generateTableData = (data) => {
    if(!data) return
    //判断data是否是对象
    if (typeof data !== 'object') {
        console.log('data不是对象', data)
        return
    }
    //循环tableData，更新value
    tableData.value.forEach(item => {
        if (data.hasOwnProperty(item.identifier)) {
            item.value = data[item.identifier]
        } else {
            item.value = '' // 如果没有对应的值，设置为空
        }
    })
    //更新时间
    update_time.value = data.create_time || data.update_time || new Date().toLocaleString()
    console.log('图表数据', tableData.value)
}

// 监听后台数据变化
watch(
    () => dataStore.devProps[props.dev_id],
    (newData) => {
        if (!newData) return
        //newData是数组，取最后一组
        generateTableData(dataStore.devProps[props.dev_id].slice(-1)[0])
    },
    { deep: true, immediate: true }
)

// 监听设备id变化
watch(
    () => props.dev_id,
    async (newId) => {
        if (newId) {
            chartProps.value=await getDeviceChartProps(newId) 
            tableData.value=chartProps.value
            generateTableData(dataStore.devProps[newId].slice(-1)[0])
        }
    },
    { immediate: true }
)


</script>

<style scoped>
.prop-name {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.prop-value {
  color: var(--el-text-color-primary);
  font-size: 14px;
  font-weight: bold;
  margin-right: 4px;
}

.prop-unit {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}
.table-header {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  margin-bottom: 10px;
}
.table-title {
  font-size: 16px;
  color: var(--el-text-color-primary);
  font-weight: bold;
}
.data-time {
  text-align: right;
  color: var(--el-text-color-secondary);
  font-size: 12px;
  margin-bottom: 0px;
}


</style>