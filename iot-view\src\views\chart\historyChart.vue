<template>
    <!-- 历史数据图表，需给出数据、图表类型、图表系列 -->
    <div class="dev-charts">
        <div class="chart-title">
            {{ props.chart_name }}
        </div>
        <v-chart class="chart" :option="chartOption" autoresize />
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import VChart from 'vue-echarts'
import * as echarts from "echarts"

const props = defineProps({
    // 图表数据，格式为对象数组
    data: {
        type: Array,
        default: () => []
    },
    //图表系列,格式为对象数组
    //[{identifier: 'speed', name: '洗涤速度', type: 'line'},
    // {identifier: 'level', name: '水位', type: 'line'}]
    chart_series: {
        type: Array,
        default: () => []
    },
    //图表名称
    chart_name: {
        type: String,
        default: ''
    }
})

const chartData = ref([])
const chartProps = ref([])
// 图表配置
const chartOption = computed(() => ({
    title: {// 图表标题
        text: props.chart_name
    },
    tooltip: {// 提示框
        show: true,
        trigger: 'axis',

    },
    legend: {//曲线的图示
        data: chartProps.value?.map(prop => prop.name),
        top: '8%'
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',  // 增加底部空间，为数据区域滚动条留出位置
        containLabel: true
    },
    dataZoom: [
        {
            type: 'slider',  // 滑动条型数据区域缩放组件
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
            bottom: '3%'  // 放置在底部
        },
        {
            type: 'inside',  // 内置型数据区域缩放组件
            xAxisIndex: [0],
            start: 0,
            end: 100
        }
    ],
    toolbox: {
        feature: {
            dataZoom: {  // 数据区域缩放
                yAxisIndex: 'none'
            },
            restore: {},  // 还原            
            magicType: {  // 动态类型切换
                type: ['line', 'bar', 'stack']
            }
        },
        right: '5%'
    },
    xAxis: {// X轴
        type: 'time',

    },
    yAxis: {// Y轴
        type: 'value',
        splitLine: {
            show: true
        }
    },
    dataset: {// 数据集
        dimensions: ['create_time', ...chartProps.value?.map(prop => prop.identifier)],
        source: chartData.value || []
    },
    series: chartProps.value?.map(prop => ({// 数据系列
        type: prop.type || 'line',
        smooth: true,
        name: prop.name,
        encode: {
            x: 'create_time',
            y: prop.identifier
        }
    }))
}))

//监听data变化
watch(() => props.data, (newVal) => {
    chartData.value = newVal
    console.log('图表数据', newVal)
},
    { deep: true, immediate: true })

watch(() => props.chart_series, (newVal) => {
    chartProps.value = newVal
},
    { deep: true, immediate: true })

</script>

<style scoped>
.dev-charts {
    width: 100%;
    height: 100%;
}

.chart {
    width: 100%;
    height: 400px;
}
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}


.chart-title {
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}
</style>
