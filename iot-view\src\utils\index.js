export function localGet(key) {
  const value = window.localStorage.getItem(key)
  try {
    return JSON.parse(window.localStorage.getItem(key))
  } catch (error) {
    return value
  }
}
export function localGetToken() {
  return localGet('token')
}

export function localSet(key, value) {
  window.localStorage.setItem(key, JSON.stringify(value))
}

export function localRemove(key) {

  //localStorage.removeItem(key)
  window.localStorage.removeItem(key)
}

export function setCookie(key, value, day) {
  let date = new Date()
  date.setDate(date.getDate() + day)
  document.cookie = key + '=' + value + ';expires=' + date
}

export function getCookie(name) {
  let reg = RegExp(name + '=([^;]+)')
  let arr = document.cookie.match(reg)
  if (arr) {
    return arr[1]
  } else {
    return ''
  }
}

export function getCsrfKey() {
  return getCookie('csrftoken')
}

export function delCookie(c_name) {
  setCookie(c_name, "", -1)
}

export function formatTime(str){
  let date = new Date(str);      
  return date.getFullYear()+'-'+(date.getMonth() + 1)+'-'+date.getDate()+' '+date.getHours()+':'+date.getMinutes()
}

export const pathMap = {
  login: '登录',
  introduce: '系统介绍',
  dashboard: '大盘数据',
  add: '添加商品',
  swiper: '轮播图配置',
  hot: '热销商品配置',
  new: '新品上线配置',
  recommend: '为你推荐配置',
  category: '分类管理',
  level2: '分类二级管理',
  level3: '分类三级管理',
  good: '商品管理',
  guest: '会员管理',
  order: '订单管理',
  order_detail: '订单详情',
  account: '修改账户'
}