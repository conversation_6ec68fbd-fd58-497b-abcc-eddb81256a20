<template>
    <!-- 产品新建或编辑表单 -->
    <div class="property-form">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" size="default">
            <el-form-item label="产品名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入产品名称"></el-input>
            </el-form-item>
            <el-form-item label="产品型号" prop="model">
                <el-input v-model="formData.model" placeholder="请输入产品型号"></el-input>
            </el-form-item>
            
            <el-form-item label="产品类别" prop="category">
                <el-select v-model="formData.category" placeholder="选择产品类别">
                    <el-option label="生产类" value="PRODUCTION"></el-option>
                    <el-option label="仪表类" value="METER"></el-option>
                    <el-option label="网关类" value="GATEWAY"></el-option>
                    <el-option label="其它" value="OTHER"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="产品类型" prop="type">
                <el-input v-model="formData.type" placeholder="水洗、烘干、熨烫、折叠、蒸汽表"></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
                <el-input v-model="formData.remark" ></el-input>
            </el-form-item>
        </el-form>
        <div class="footer">
            <el-button type="primary" @click="submitForm">{{ isEdit ? '更新' : '新建' }}</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { updateProductApi,getProductsApi } from '@/api/products'

const props = defineProps({
    pro_id: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['success'])

const formRef = ref(null)
const isEdit = ref(false)

// 表单数据
const formData = ref({
    name: '',
    model: '',
    category: '',
    type: '',
    brand:'',
    remark: '',
})

// 表单验证规则
const rules = {
    name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
    category: [{ required: true, message: '请选择产品类别', trigger: 'change' }],
    type: [{ required: true, message: '请输入产品类型', trigger: 'blur' }],
}

watch(() => props.pro_id, async (newVal) => {
    console.log('激活编辑窗口', newVal)
    // 重置表单
    formData.value = {
        id: '',
        name: '',
        model: '',
        category: '',
        type: '',
        brand:'',
        remark: ''
    }
    isEdit.value = false
    if (newVal) {
        isEdit.value = true
        // 获取产品详情
        const res = await getProductsApi(newVal)
        formData.value = { ...res[0] }
    }
}, { immediate: true })


// 提交表单
const submitForm = async () => {
    if (!formRef.value) return

    try {
        await formRef.value.validate()
        //向后台提交数据,格式化
        const data={
            id: formData.value.pro_id,
            name: formData.value.name,
            model: formData.value.model,
            category: formData.value.category,
            type: formData.value.type,
            brand: formData.value.brand,
            remark: formData.value.remark
        }
        const res=await updateProductApi(data)
        if (res.status === 'error') {
            ElMessage.error('操作失败: ' + res.message)
            return
        }
        ElMessage.success('更新成功')
        // 通知父组件刷新列表
        formRef.value.resetFields()
        emit('success')
    } catch (error) {
        ElMessage.error(isEdit.value ? '更新失败' : '添加失败')
        console.error('操作失败:', error)
    }
}

</script>
<style scoped>
.property-form {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
}

.header {
    margin-bottom: 20px;
}

.el-form-item {
    .el-input {
        width: 300px;
        height: 40px;
    }

    .el-select {
        width: 300px;
        height: 40px;
    }
}

.rule-form {
    max-width: 600px;
}
</style>