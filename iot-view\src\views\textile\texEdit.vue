<!-- 新建或编辑纺织品 -->
<template>
    <div class="rule-add">
        <!-- 表单内容 -->
        <el-form ref="formRef" :model="formData" :rules="rules" size="default" label-width="120px" class="rule-form" label-position="top">
            <!-- 规则名称 -->
            <el-form-item label="纺织品名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入纺织品名称" />
            </el-form-item>
            <!-- 纺织品ID,当编辑时，纺织品ID不可编辑 -->
            <el-form-item label="纺织品ID" prop="tex_id">
                <el-input v-model="formData.tex_id" placeholder="请输入纺织品ID" :disabled="isEdit" />
            </el-form-item>
            <!-- 所属酒店 -->
            <el-form-item label="所属酒店" prop="hotel_id">
                <el-input v-if="hotel" v-model="hotel.name"  disabled />
                <el-select v-else v-model="formData.hotel_id" placeholder="请选择所属酒店">
                    <el-option v-for="hotel in hotels" :key="hotel.id" :label="hotel.name" :value="hotel.id" />
                </el-select>
            </el-form-item>
            <!-- 洗涤价格,浮点数字格式 -->
            <el-form-item label="洗涤价格" prop="price">
                <el-input-number v-model="formData.price" placeholder="请输入洗涤价格" :precision="2" :step="0.01" :max="99" />
            </el-form-item>
            <!-- 赔偿价格,浮点数字格式 -->
            <el-form-item label="赔偿价格" prop="pay_price">
                <el-input-number v-model="formData.pay_price" placeholder="请输入赔偿价格" :min="1" :max="999" />
            </el-form-item>
        </el-form>
        <div class="footer">
            <el-button type="primary" @click="submitForm">{{ submitButtonText }}</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch,defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { useTextileStore } from '@/stores/modules/textile';
import { updateTextileApi } from '@/api/textile';

const texStore = useTextileStore()
const emit = defineEmits(['close'])
const props = defineProps({
    tex_id: {
        type: String,
    },
    hotel_id: {
        type: String,        
    }
})

// 表单数据
const formData = ref({
    name: '',
    tex_id: '',
    hotel_id: '',
    price: '',
    pay_price: '',
})

// 表单验证规则
const rules = {
    name: [{ required: true, message: '请输入纺织品名称', trigger: 'blur' }],
    tex_id: [{ required: true, message: '请输入纺织品ID', trigger: 'blur' }],
    hotel_id: [{ required: true, message: '请输入所属酒店', trigger: 'blur' }],
    price: [{ required: true, message: '请输入价格', trigger: 'blur' }]
}

const formRef = ref(null)

// 修改提交按钮文本
const submitButtonText = computed(() => isEdit.value ? '更新纺织品' : '新增纺织品')

// 页面标题计算属性
const pageTitle = computed(() => isEdit.value ? '编辑纺织品' : '新增纺织品')
// 是否编辑模式
const isEdit = ref(false)
// 获取纺织品详情
const fetchTexDetail = async (tex_id) => {
    try {
        const texDetail = await texStore.getTextileInfo(tex_id)
        console.log('texDetail', texDetail)
        return texDetail
    } catch (error) {
        ElMessage.error('获取纺织品详情失败')
    }
}

watch(() => props.tex_id, async (newVal) => {
    if (newVal) {
        isEdit.value = true
        const texDetail = await fetchTexDetail(newVal)
        Object.assign(formData.value, texDetail)

    }else{
        isEdit.value = false
        formData.value = {
            name: '',
            tex_id: '',
            hotel_id: '',
            price: '',
        }
    }
}, { immediate: true })

const hotel=ref(null)
const hotels=ref([])
watch(() => props.hotel_id, async (newVal) => {
    hotel.value = newVal
    formData.value.hotel_id = newVal
    if (newVal) {//指定了酒店id，则直接赋值
        hotel.value = await texStore.requestHotel(newVal)        
    }else{
        hotels.value = await texStore.requestHotels()
    }
}, { immediate: true })

// 初始化加载
onMounted(async () => {    
    
})

// 提交表单
const submitForm = async () => {    
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
        if (valid) {
            try {
                console.log('提交的纺织品表单', formData.value)
                await updateTextileApi(formData.value)
                ElMessage.success('纺织品更新成功')
                emit('close')
            } catch (error) {
                ElMessage.error(isEdit.value ? '纺织品更新失败' : '纺织品创建失败')
            }
        }
    })
}


</script>

<style scoped>
.rule-add {
    padding: 20px;
    height: 100%;
    overflow-y: auto;    
}

.header {
    margin-bottom: 20px;
}

.rule-form {
    max-width: 600px;
}

.el-dialog__body {
    padding: 20px;
    background-color: #1e1e1e;
}

.condition-settings {
    margin: 20px;
}

.condition-row {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.el-form-item {
    .el-input {
        width: 300px;
        height: 32px;
    }

    .el-select {
        width: 300px;
        height: 32px;
    }
}
</style>
