<!-- 首页-概览页面，有设备状态饼图、 -->
<template>
    <!-- WebSocket连接状态 -->
    <div>
        <el-tag :type="socketStatusType" class="status-tag" size="small">
            SOCKET实时数据{{socket.isConnectedRef.value?'已连接':'未连接'}}
        </el-tag>
        
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import socket from '@/utils/socket'

// 连接状态标签类型
const socketStatusType = computed(() => socket.isConnectedRef.value ? 'success' : 'danger');

</script>

<style scoped>
.socket-status {
    margin-bottom: 20px;
}

.status-tag {
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

:deep(.el-icon) {
    margin-right: 2px;
}
</style>
