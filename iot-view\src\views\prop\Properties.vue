<template>
  <!-- 属性列表，带最新一次数据 -->
  <div>
    <div class="button-group">
      <el-button type="primary" @click="fetchAttributes">刷新</el-button>
      <el-button type="success" @click="sendAttributes">下发属性</el-button>
    </div>
    <el-table :data="attributes" style="width: 100%">
      <el-table-column type="selection" width="55"></el-table-column>

      <el-table-column prop="name" label="属性名称"></el-table-column>
      <el-table-column prop="identifier" label="属性标识" width="180"></el-table-column>
      <el-table-column prop="value" label="属性值"></el-table-column>
      <el-table-column prop="updateTime" label="更新时间" width="180"></el-table-column>
      <el-table-column label="历史数据">
        <template #default="scope">
          <el-button type="text" @click="viewHistory(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>    
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElButton } from 'element-plus';
import { useProductsStore } from '@/stores/modules/products';
import { getDevProcessDetailLatestApi } from '@/api/data';

const props = defineProps({
  dev_id: {
    type: String,
    required: true
  },
  pro_id: {
    type: String,
    required: true
  }
});
console.log(props);
const pdStore = useProductsStore();
const attributes = ref([]);
const historyRef = ref(null);

//const dev_id=ref(props.dev_id)

// 获取设备的属性列表
const fetchAttributes = async () => {
  const dev_props = await pdStore.requestDevProps(props.dev_id);
  console.log('获取设备属性', dev_props);
  attributes.value=dev_props

  if (attributes.value.length==0) return
  //获取设备的最新数据
  const latestData = await getDevProcessDetailLatestApi(props.dev_id);
  const {data,create_time} = latestData
  console.log('获取设备最新数据', data);
  //把data中的属性值赋值给dev_props的value
  attributes.value.forEach(item => {
    item.value = data[item.identifier];
    item.updateTime = create_time;
  });
  console.log('更新后的属性列表', attributes.value); 

};


onMounted(() => {
  fetchAttributes();
});

const sendAttributes = () => {
  // 实现下发属性的逻辑
  console.log('Sending attributes...');
};

const viewHistory = (attribute) => {  
  historyRef.value?.open(attribute);
  
};

const viewChart = (attribute) => {
  // 实现查看图表的逻辑,弹出一个echarts曲线图
  console.log('Viewing chart for attribute:', attribute);
};

</script>

<style scoped>
.button-group {
  margin-bottom: 10px;
}

</style>
