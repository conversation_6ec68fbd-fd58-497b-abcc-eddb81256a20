import { defineStore } from 'pinia'
import { constantRoutes, asyncRoutes, resetRouter } from '@/router'
import { localSet, localGet, localRemove } from '@/utils'
import { loginApi, getUserInfoApi, getTokenApi, logoutApi } from '@/api/user'

// 定义初始状态
const initialState = {
    userInfo: {
        username: '',
        roles: [],
        avatar: '',
        email: ''
    },
    token: localGet('sessionid') || null,
    csrfToken: localGet('csrftoken') || null,
    routes: [],
    isAuthenticated: !!localGet('sessionid')
}


export const useAccount = defineStore('account', {
    state: () => ({ ...initialState }),

    getters: {
        // 获取用户角色
        userRoles: (state) => state.userInfo.roles || [],
        // 判断是否已登录
        isLoggedIn: (state) => state.isAuthenticated,
        // 获取用户名
        username: (state) => state.userInfo.username,
        //判断用户是否有指定角色
        hasRole: (state) => (role) => {
            return state.userInfo.roles.includes(role)
        },
        //判断用户是否有指定权限
        hasPermission: (state) => (permission) => {
            return state.userInfo.permissions.includes(permission)
        },
    },

    actions: {
        // 重置状态
        resetState() {
            Object.assign(this, initialState)
        },

        // 登录
        async login(credentials) {
            try {
                const { username, password, remember } = credentials

                // 调用登录 API
                const response = await loginApi(credentials)

                // 设置登录状态
                this.isAuthenticated = true
                localSet('sessionid', 'hasLogin')

                // 如果需要记住登录状态
                if (remember) {
                    localSet('remember_user', username)
                } else {
                    localRemove('remember_user')
                }

                // 获取用户信息
                await this.getUserInfo()

                return response
            } catch (error) {
                this.isAuthenticated = false
                localRemove('sessionid')
                throw error
            }
        },

        // 登出
        async logout() {
            try {
                // 这里可以添加调用登出 API 的逻辑
                await logoutApi()
                // 清除状态
                this.resetState()
                localRemove('sessionid')
                localRemove('csrftoken')
                resetRouter()
                
                

            } catch (error) {
                console.error('登出失败:', error)
                throw error
            }
        },

        // 获取用户信息
        async getUserInfo() {
            try {
                const data = await getUserInfoApi()
                console.log('获取用户信息', data)
                if (!data) {
                    throw new Error('获取用户信息失败')
                }

                if (!data.roles || !Array.isArray(data.roles) || !data.roles.length) {
                    throw new Error('用户角色信息无效')
                }

                this.userInfo = {
                    ...this.userInfo,
                    ...data
                }

                // 生成路由
                const accessRoutes = await this.generateRoutes(data.roles)
                return { ...data, accessRoutes }

            } catch (error) {
                console.error('获取用户信息失败:', error)
                throw error
            }
        },

        // 获取 CSRF Token
        async getToken() {
            try {
                const { csrftoken } = await getTokenApi()
                this.csrfToken = csrftoken
                localSet('csrftoken', csrftoken)
                return csrftoken
            } catch (error) {
                console.error('获取CSRF Token失败:', error)
                throw error
            }
        },

        // 生成路由
        async generateRoutes(roles) {
            let accessedRoutes = []

            if (roles.includes('admin')) {
                accessedRoutes = asyncRoutes
            } else {
                accessedRoutes = this.filterAsyncRoutes(asyncRoutes, roles)
            }

            this.routes = [...constantRoutes, ...accessedRoutes]
            return accessedRoutes
        },

        // 过滤异步路由
        filterAsyncRoutes(routes, roles) {
            return routes.filter(route => {
                if (this.hasPermission2(roles, route)) {
                    if (route.children) {
                        route.children = this.filterAsyncRoutes(route.children, roles)
                    }
                    return true
                }
                return false
            })
        },

        // 检查权限
        hasPermission2(roles, route) {
            if (route.meta?.roles) {
                return roles.some(role => route.meta.roles.includes(role))
            }
            return true
        }
    }
})