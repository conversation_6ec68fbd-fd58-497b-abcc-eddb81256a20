<!-- 布草收发记录 -->
<template>
    <div :class="{ 'tex-list': !props.hotel_id }">
        <!-- 标题栏 -->
        <div class="header">
            <div class="title">布草收发记录</div>            
        </div>
        <!-- 时间选择和操作按钮 -->
        <div class="toolbar">
            <el-date-picker v-model="timeRange" type="datetimerange" range-separator="至" start-placeholder="开始时间"
                end-placeholder="结束时间" :default-time="defaultTime" value-format="YYYY-MM-DD HH:mm:ss"
                @change="getTexRecords" />
            <div class="buttons">
                <!-- 新增收发记录 -->
                <el-button type="primary" @click="handleAdd('IN')">新增收件</el-button>
                <el-button type="primary" @click="handleAdd('OUT')">新增送货</el-button> 
                <el-button type="primary" @click="handleAdd('PAY')">新增赔偿</el-button>                
                <el-button type="success" :icon="Download" @click="exportData">导出</el-button>
            </div>
        </div>
        <!-- 布草收发记录列表 -->
        <el-table v-loading="loading" :data="texRecords" border style="width: 100%">
            <el-table-column prop="create_time" label="收发时间" min-width="200" sortable />
            <el-table-column prop="type" label="类型" min-width="120" sortable>
                <template #default="{ row }">
                    <el-tag :type="recordTypeTag(row)" effect="dark">{{ recordTypeName(row) }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="tex_id" label="布草ID" min-width="100" />
            <el-table-column prop="tex_name" label="布草名称" min-width="200" />
            <el-table-column prop="hotel_name" label="酒店名称" min-width="200" />
            <el-table-column prop="quantity" label="数量" min-width="100" /> 
            <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                    <el-button-group>
                        <el-button size="small" :icon="Edit" @click="handleEdit(row)"></el-button>                        
                    </el-button-group>
                </template>
            </el-table-column>
        </el-table>
        <!-- 新增纺织品记录弹窗 -->
        <el-dialog v-model="editTexDialog" width="30%">
            <TexRecordEdit :record="currentRecord" :type="recordType" @close="handleClose" />
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Download } from '@element-plus/icons-vue'
import { useTextileStore } from '@/stores/modules/textile'
import { getTexRecordsApi } from '@/api/textile'
import TexRecordEdit from './texRecordEdit.vue'

//可以接收props
const props = defineProps({
    hotel_id: {
        type: String,
        default: ''
    }
})
const texStore = useTextileStore()
const timeRange = ref([])
const defaultTime = [
    new Date(2025, 1, 1, 0, 0, 0),
    new Date(2025, 1, 1, 23, 59, 59)
]

const loading = ref(false)
//布草收发记录
const texRecords = ref([])
const getTexRecords = async () => {
    let d = { start_time: timeRange.value[0], end_time: timeRange.value[1] }
    if (props.hotel_id) {
        d.hotel_ids = [props.hotel_id]
    }
    const res = await getTexRecordsApi(d)
    //添加对应信息
    res.forEach(item => {
        const tex = texStore.textiles.find(tex => tex.tex_id === item.tex_id)
        item.tex_name = tex.name
        item.hotel_name = tex.hotel_name
    })
    //按create_time、hotel_name、tex_id排序
    res.sort((a, b) => {
        if (a.create_time !== b.create_time) {
            return new Date(a.create_time)-new Date(b.create_time)
        }
        return a.hotel_name.localeCompare(b.hotel_name) || a.tex_id.localeCompare(b.tex_id)
    })
    texRecords.value = res
}


// 新增纺织品弹窗
const editTexDialog = ref(false)
// 新增收发记录
const recordType = ref('IN')
const handleAdd = (type) => {
    currentRecord.value = null
    recordType.value = type
    editTexDialog.value = true
}

// 编辑收发记录
const currentRecord = ref(null)
const handleEdit = (row) => {
    currentRecord.value = row
    editTexDialog.value = true
}

// 关闭新增纺织品弹窗
const handleClose = () => {
    console.log('关闭新增纺织品弹窗')
    editTexDialog.value = false
}
//收发类型tag
const recordTypeTag = (row) => {
    const typeMap = {
        IN: 'primary',
        OUT: 'info',
        PAY: 'danger'
    }
    return typeMap[row.type]
}
//收发类型名称
const recordTypeName = (row) => {
    const typeMap = {
        IN: '收件',
        OUT: '送货',
        PAY: '赔偿'
    }
    return typeMap[row.type]
}

// 初始化
onMounted(async () => {
    await texStore.requestTextiles()
    //设置默认时间为本月1号到今天
    const end = new Date()
    const start = new Date()    
    start.setDate(1)

    const endTime = formatDate(end)
    const startTime = formatDate(start)

    timeRange.value = [startTime, endTime]
    await getTexRecords()
})

//转格式化时间,YYYY-MM-DD HH:mm:ss
const formatDate = (date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    const second = date.getSeconds().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}
</script>

<style scoped>
.tex-list {
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.title {
    font-size: 20px;
    font-weight: bold;
}
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;
    /* 防止工具栏被压缩 */
}
.buttons {
    padding-left: 20px;
    display: flex;
    gap: 10px;
}


:deep(.el-table .cell) {
    white-space: nowrap;
}
</style>
