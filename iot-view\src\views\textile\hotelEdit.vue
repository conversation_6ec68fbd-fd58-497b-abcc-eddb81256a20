<!-- 新建或编辑酒店 -->
<template>
    <div class="rule-add">
        <!-- 表单内容 -->
        <el-form ref="formRef" :model="formData" :rules="rules" size="default" label-width="120px" class="rule-form">
            <!-- 规则名称 -->
            <el-form-item label="酒店名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入酒店名称" />
            </el-form-item>
            <!-- 酒店ID,当编辑时，酒店ID不可编辑 -->
            <el-form-item label="酒店ID" prop="hotel_id">
                <el-input v-model="formData.hotel_id" placeholder="请输入酒店ID" :disabled="isEdit" />
            </el-form-item>
            <!-- 酒店状态，新增时，酒店状态为启用 -->
            <el-form-item label="酒店状态" prop="status">
                <el-switch v-model="formData.status" :active-value="1" :inactive-value="0" width="60" inline-prompt
                    active-text="正常" inactive-text="停用" />
            </el-form-item>
            <!-- 联系人 -->
            <el-form-item label="联系人" prop="contact_name">
                <el-input v-model="formData.contact_name" placeholder="请输入联系人" />
            </el-form-item>
            <!-- 联系电话 -->
            <el-form-item label="联系电话" prop="contact_phone">
                <el-input v-model="formData.contact_phone" placeholder="请输入联系电话" />
            </el-form-item>
            <!-- 酒店地址 -->
            <el-form-item label="酒店地址" prop="address">
                <el-input v-model="formData.address" placeholder="请输入酒店地址" />
            </el-form-item>
        </el-form>
        <div class="footer">
            <el-button type="primary" @click="submitForm">{{ submitButtonText }}</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useTextileStore } from '@/stores/modules/textile';
import { updateHotelApi } from '@/api/textile';
import { useRouter } from 'vue-router'
const texStore = useTextileStore()
const router = useRouter()
const props = defineProps({
    hotel_id: {
        type: String,
        required: true
    }
})

// 表单数据
const formData = ref({
    name: '',
    hotel_id: '',
    status: 1,
    contact_name: '',
    contact_phone: '',
    address: ''
})

// 表单验证规则
const rules = {
    name: [{ required: true, message: '请输入酒店名称', trigger: 'blur' }],
    hotel_id: [{ required: true, message: '请输入酒店ID', trigger: 'blur' }],
    contact_name: [{ required: false, message: '请输入联系人', trigger: 'blur' }],
    contact_phone: [{ required: false, message: '请输入联系电话', trigger: 'blur' }],
    address: [{ required: false, message: '请输入酒店地址', trigger: 'blur' }]
}

const formRef = ref(null)

// 修改提交按钮文本
const submitButtonText = computed(() => isEdit.value ? '更新酒店' : '新增酒店')

// 页面标题计算属性
const pageTitle = computed(() => isEdit.value ? '编辑酒店' : '新增酒店')
// 是否编辑模式
const isEdit = ref(false)
// 获取酒店详情
const fetchHotelDetail = async (hotel_id) => {
    try {
        const hotelDetail = await texStore.getHotelInfo(hotel_id)
        return hotelDetail

    } catch (error) {
        ElMessage.error('获取酒店详情失败')
    }
}

watch(() => props.hotel_id, async (newVal) => {
    if (newVal) {
        isEdit.value = true
        const hotelDetail = await fetchHotelDetail(newVal)
        Object.assign(formData.value, hotelDetail)
    }else{
        isEdit.value = false
        formData.value = {
            name: '',
            hotel_id: '',
            status: 1,
            contact_name: '',
            contact_phone: '',
            address: '',
        }
    }
}, { immediate: true })

// 初始化加载
onMounted(async () => {
    
})

// 提交表单
const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
        if (valid) {
            try {
                console.log('提交的酒店表单', formData.value)
                await updateHotelApi(formData.value)
                ElMessage.success('酒店更新成功')
                router.back()
            } catch (error) {
                ElMessage.error(isEdit.value ? '酒店更新失败' : '酒店创建失败')
            }
        }
    })
}
</script>

<style scoped>
.rule-add {
    padding: 20px;
    height: 100%;
    overflow-y: auto;

}

.header {
    margin-bottom: 20px;
}

.rule-form {
    max-width: 800px;

}

.el-dialog__body {
    padding: 20px;
    background-color: #1e1e1e;
}

.condition-settings {
    margin: 20px;
}

.condition-row {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.el-form-item {
    .el-input {
        width: 300px;
        height: 32px;
    }

    .el-select {
        width: 300px;
        height: 32px;
    }
}
</style>
