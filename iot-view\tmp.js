router.beforeEach(async (to, from, next) => {
    // 设备页面标题
    document.title = pathMap[to.name]

    // 获取token
    const hasToken = localGet('token')

    if (hasToken) {
        if (to.path === '/login') {
            // 如果访问登录页面，重定向到首页
            next({ path: '/' })
        } else {
            //检查用户信息是否获取，如没有则去获取
            const store = useMainStore()
            const hasRoles = store.userInfo.roles && store.userInfo.roles.length > 0
            if (hasRoles) {
                next()
            } else {
                try {
                    //获取用户信息
                    const { roles } = await getUserInfo()
                    //动态生成路由

                    //挂载路由

                    next({ ...to, replace: true })
                } catch (error) {
                    // remove token and go to login page to re-login
                    //await store.dispatch('user/resetToken')          
                    next(`/login?redirect=${to.path}`)
                }

            }
        }
    } else {
        //用户未登录
        if (whiteList.indexOf(to.path) !== -1) {
            // in the free login whitelist, go directly
            next()
        } else {
            // other pages that do not have permission to access are redirected to the login page.
            next(`/login?redirect=${to.path}`)

        }
    }
})