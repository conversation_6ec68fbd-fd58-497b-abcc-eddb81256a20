# 洗涤设备物联网平台

一个基于 Vue3 + Django 的洗涤设备物联网监控平台，用于实时监控洗涤设备状态、收集设备数据、生成报表分析。

## 📋 项目概述

本平台主要用于洗涤行业的设备管理和数据分析，支持多种洗涤设备（水洗机、烘干机、折叠机等）的实时监控，提供完整的数据采集、存储、分析和可视化解决方案。

### 核心功能

- **实时设备监控**: 通过 MQTT/WebSocket 实时获取设备状态和运行数据
- **数据聚合分析**: 支持按时间、设备、纺织品类型等多维度数据统计
- **报表生成**: 自动生成设备产量、状态分析等各类报表
- **用户权限管理**: 多角色用户管理，支持客户数据隔离
- **大屏展示**: 提供实时数据大屏显示功能

## 🏗️ 技术架构

### 前端技术栈
- **Vue 3**: 渐进式 JavaScript 框架
- **Element Plus**: Vue 3 UI 组件库
- **Pinia**: Vue 状态管理
- **Vue Router**: 路由管理
- **ECharts**: 数据可视化图表
- **Vite**: 构建工具

### 后端技术栈
- **Django 4.x**: Python Web 框架
- **Django Channels**: WebSocket 支持
- **Django REST Framework**: API 开发
- **SQLite/PostgreSQL**: 数据库
- **Redis**: 缓存和消息队列
- **APScheduler**: 定时任务调度

### 通信协议
- **MQTT**: 设备数据上报
- **WebSocket**: 实时数据推送
- **SSE**: 服务器推送事件
- **RESTful API**: 标准 HTTP 接口

## 📁 项目结构

```
Washing-IoT/
├── iot-view/                 # 前端 Vue3 应用
│   ├── src/
│   │   ├── views/           # 页面组件
│   │   ├── components/      # 公共组件
│   │   ├── stores/          # Pinia 状态管理
│   │   ├── api/             # API 接口
│   │   └── utils/           # 工具函数
│   ├── package.json
│   └── vite.config.js
│
└── iotcloud/                # 后端 Django 服务
    ├── Apps/                # 应用模块
    │   ├── users/           # 用户模块
    │   ├── products/        # 设备模块
    │   ├── data/            # 数据模块
    │   ├── textile/         # 纺织品模块
    │   ├── aggregate/       # 数据聚合模块
    │   ├── task/            # 定时任务模块
    │   └── web/             # Web服务模块
    ├── IotView/             # 项目配置
    ├── manage.py
    └── requirements.txt
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Node.js 16+
- Redis 6+
- SQLite/PostgreSQL

### 后端部署

1. **创建虚拟环境**
```bash
cd iotcloud
python -m venv .venv
.venv\Scripts\activate  # Windows
# source .venv/bin/activate  # Linux/Mac
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **数据库迁移**
```bash
python manage.py makemigrations
python manage.py migrate
```

4. **创建超级用户**
```bash
python manage.py createsuperuser
```

5. **启动服务**
```bash
python manage.py runserver 8000
```

### 前端部署

1. **安装依赖**
```bash
cd iot-view
npm install
```

2. **开发环境启动**
```bash
npm run dev
```

3. **生产环境构建**
```bash
npm run build:release
```

## 📊 核心模块

### 1. 设备模块 (products)
- 设备型号管理
- 设备实例管理
- 设备分组和权限控制
- 设备状态监控

### 2. 数据模块 (data)
- 实时数据采集和存储
- 设备程序数据记录
- 设备事件和报警管理
- 设备状态变化追踪

### 3. 用户模块 (users)
- 多角色用户管理 (管理员/客户管理员/员工等)
- 客户数据隔离
- 权限控制和认证

### 4. 纺织品模块 (textile)
- 纺织品信息管理
- 酒店客户管理
- 洗涤价格配置

### 5. 数据聚合模块 (aggregate)
- 设备产量统计
- 纺织品洗涤量分析
- 多维度数据报表生成
- 时间序列数据分析

## 🔌 数据接口

### MQTT 数据格式
```json
{
  "cid": 1,                    // 客户ID
  "did": "WSH25001",          // 设备ID
  "gid": "G204001606",        // 网关ID
  "points": {                 // 数据点
    "temperature": 65.5,
    "pressure": 2.1,
    "status": "RUN"
  },
  "time": "2023/05/18 14:14:48"
}
```

### WebSocket 实时推送
- 设备状态变化通知
- 实时数据推送
- 报警信息推送
- 心跳检测

## 📈 数据可视化

- **实时监控大屏**: 设备状态总览、产量统计
- **设备状态图表**: 饼图、柱状图、趋势图
- **生产报表**: 按时间、设备、纺织品类型统计
- **设备利用率分析**: 运行时间、故障统计

## 🔐 权限管理

### 角色定义
- **系统管理员**: 全系统权限
- **客户管理员**: 客户范围内管理权限
- **部门主管**: 部门设备管理权限
- **普通员工**: 基础查看权限
- **VIP用户**: 特殊查看权限

### 数据隔离
- 基于客户ID的数据隔离
- 用户只能访问所属客户的数据
- API 接口自动过滤客户数据

## 🛠️ 开发指南

### 添加新的应用模块
```bash
cd iotcloud/Apps
python ../manage.py startapp 模块名
```

### 数据库操作
```bash
# 检查迁移状态
python manage.py showmigrations

# 创建迁移文件
python manage.py makemigrations

# 执行迁移
python manage.py migrate
```

### 前端开发
- 使用 Element Plus 组件库
- 遵循 Vue 3 Composition API 规范
- 使用 Pinia 进行状态管理
- API 请求统一使用 axios

## 📝 API 文档

主要 API 端点：

- `/users/` - 用户管理
- `/products/` - 设备管理
- `/data/` - 数据查询
- `/textile/` - 纺织品管理
- `/aggregate/` - 数据聚合
- `/websocket/data/` - WebSocket 连接

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请联系项目维护者。

---

**注意**: 本项目为洗涤设备物联网监控平台，适用于工业洗涤设备的实时监控和数据分析场景。
