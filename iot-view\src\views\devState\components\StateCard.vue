<!-- 设备状态卡片 -->
<template>
    <el-card shadow="hover" :body-style="{ padding: '0px' }" style="background-color:transparent;border-width: 0;">
        <header style="text-align: center;padding: 5px;">
            <el-text  size="small" style="font-weight: bold;">
                {{ deviceInfo.name +"-"}}{{ deviceState.name }}
            </el-text>            
        </header>
        <div class="iconbox">       
            <img :src="deviceInfo.icon?`/images/${deviceInfo?.icon}`:'/images/sailstar.png'" alt="" class="devicon" :class="deviceState.class">
        </div>
    </el-card>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useDataStore } from '@/stores/modules/data'
import { useProductsStore } from '@/stores/modules/products'
const dataStore = useDataStore()
const pdStore = useProductsStore()

const props = defineProps({
    dev_id: {
        type: String,
        required: true
    }
})
const deviceInfo=ref({})
//获取设备信息
const getDeviceInfo=async()=>{
    let info=await pdStore.getDevInfo(props.dev_id)    
    deviceInfo.value=info
}

// 使用计算属性监听设备状态的变化
const deviceState = computed(() => {
    const devState = dataStore.devState[props.dev_id] || {status:'OFFLINE',name:'离线',create_time:''}
    return {
        name: devState.name,
        class: getStatusClass(devState.status),
        header_class: getStatusHeaderClass(devState.status)
    }
})

// 获取状态对应的类名
const getStatusClass = (status) => {
    const statusMap = {
        OFFLINE: 'status-offline',        
        RUN: 'status-run',
        STANDBY: 'status-standby',
        MANUAL: 'status-manual',
        ALARM: 'status-alarm',
        FAULT: 'status-fault',
        OTHER: 'status-other'
    }
    return statusMap[status] || ''
    }


    // 获取状态对应的类名
const getStatusHeaderClass = (status) => {
    const statusMap = {
        OFFLINE: 'offline_status',
        RUN: 'run_status',
        STANDBY: 'standby_status',
        MANUAL: 'manual_status',
        ALARM: 'alarm_status',        
    }
    return statusMap[status] || ''
}
// 获取状态显示文本
const getStatusText = (status) => {
    const statusMap = {
        OFFLINE: '离线',        
        RUN: '运行中',
        STANDBY: '待机',
        MANUAL: '手动',
        ALARM: '报警',
        FAULT: '故障',        
        OTHER: '其他'
    }
    return statusMap[status] || '未知'
}

onMounted(async()=>{
    await getDeviceInfo()
})
</script>


<style scoped>
.iconbox {
    min-height: 100px;
    padding: 0.1rem;
}

.devicon {
    width: 100%;
    object-fit: contain;
    /* 图片缩放方式：fill拉伸/缩小填充；contain保持宽高缩放填充；cover保持宽高比填充，超出部分裁剪 */
}

.status-standby {
    transform: translateX(-200px);
    filter: drop-shadow(200px 0 0 #F56C6C)
        /* 先将原图向左偏移200，看不到；再投影出各种颜色的图案到原来位置 */
}

.status-offline {
    transform: translateX(-200px);
    filter: drop-shadow(200px 0 0 #909399)
}

.status-run {
    transform: translateX(-200px);
    filter: drop-shadow(200px 0 0 #409EFF)
}

.warning {
    transform: translateX(-200px);
    filter: drop-shadow(200px 0 0 #E6A23C)
}
/* 离线状态，模拟灰灯 */
.offline_status {
    background-color: #909399;
}
/* 运行状态,模拟背景绿灯 */
.run_status {
    background-color: #409EFF;
}
/* 待机状态,模拟背景黄灯 */
.standby_status {
    background-color: #E6A23C;
}
/* 手动状态,模拟背景绿灯 */
.manual_status {
    background-color: #409EFF;
}
/* 报警状态,模拟背景红灯 */
.alarm_status {
    background-color: #F56C6C;
}

</style>