import request from '@/utils/request'


//获取产品列表，无需参数
export function getHotelsApi(data) {
  //可选参数hotel_ids
  return request({
    url: '/textile/getHotels/',
    method: 'get',
    params: data
  })
}

export function updateHotelApi(data) {
    //把data对象改为formdata格式
    const formData = new FormData()
    for (const key in data) {
      formData.append(key, data[key])
    }
    return request({
      url: '/textile/updateHotel/',
      method: 'post',
      data: formData
    })
  }


//获取布草
export function getTextilesApi(data) {
  //参数hotel_ids,tex_ids
  return request({
    url: '/textile/getTextiles/',
    method: 'get',
    params: data
  })
}

export function updateTextileApi(data) {
  //把data对象改为formdata格式
  const formData = new FormData() 
  for (const key in data) {
    formData.append(key, data[key]) 
  }
  return request({
    url: '/textile/updateTextile/',
    method: 'post',
    data: formData 
  })
}


//删除纺织品
export function deleteTextileApi(tex_id) {
  //参数tex_id
  const formData = new FormData()
  formData.append('tex_id', tex_id)
  return request({
    url: '/textile/deleteTextile/',
    method: 'post',
    data: formData
  })
}

//获取布草收发记录
export function getTexRecordsApi(data) {
  return request({
    url: '/textile/getTexRecords/',
    method: 'get',
    params: data
  })
}

//创建布草收发记录
export function createTexRecordApi(data) {
  //把data对象改为formdata格式  
  const formData = new FormData()
  for (const key in data) {
    formData.append(key, data[key])
  }
  return request({
    url: '/textile/createTexRecord/',
    method: 'post',
    data: formData
  })
}

//更新布草收发记录
export function updateTexRecordApi(data) {
  //把data对象改为formdata格式
  const formData = new FormData()
  for (const key in data) {
    formData.append(key, data[key])
  }
  return request({
    url: '/textile/updateTexRecord/',
    method: 'post',
    data: formData
  })
}

