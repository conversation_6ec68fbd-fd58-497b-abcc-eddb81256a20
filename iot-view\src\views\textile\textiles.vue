<!-- 布草表格 -->
<template>
    <div :class="{ 'tex-list': !props.hotel_id }">
        <!-- 标题栏 -->
        <div class="header">
            <div class="title">布草信息</div>
            <el-button type="primary" @click="handleAdd">新增布草</el-button>
        </div>

        <!-- 布草列表 -->
        <el-table v-loading="loading" :data="texList" border style="width: 100%">
            <el-table-column prop="tex_id" label="布草ID" min-width="100" />
            <el-table-column prop="name" label="布草名称" min-width="200" />
            <el-table-column prop="hotel_name" label="酒店名称" min-width="200" />
            <el-table-column prop="price" label="洗涤价格" min-width="200" />
            <el-table-column prop="pay_price" label="赔偿价格" min-width="200" />            
            <el-table-column prop="update_time" label="更新时间" min-width="200" sortable />
            <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                    <el-button-group>
                        <el-button size="small" :icon="Edit" @click="handleEdit(row)"></el-button>
                        <el-button size="small" :icon="Delete" type="danger" @click="handleDelete(row)"></el-button>
                    </el-button-group>
                </template>
            </el-table-column>
        </el-table>
        <!-- 新增纺织品弹窗 -->
        <el-dialog v-model="editTexDialog" title="新增纺织品" width="40%">
            <TexEdit :hotel_id="props.hotel_id" :tex_id="currentTex" @close="handleClose" />
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Delete } from '@element-plus/icons-vue'
import { getTextilesApi,deleteTextileApi } from '@/api/textile'
import TexEdit from './texEdit.vue'

//可以接收props
const props = defineProps({
    hotel_id: {
        type: String,
        default: ''
    }
})

// 列表数据
const texList = ref([])
const loading = ref(false)

// 获取纺织品列表
const fetchData = async () => {
    loading.value = true
    let d = {}
    if (props.hotel_id) {
        d.hotel_ids = [props.hotel_id]
    }

    try {
        texList.value = await getTextilesApi(d)        
    } catch (error) {
        ElMessage.error('获取布草列表失败')
    } finally {
        loading.value = false
    }
}

// 新增纺织品弹窗
const editTexDialog = ref(false)
// 新增纺织品
const handleAdd = () => {
    currentTex.value = null
    editTexDialog.value = true
}

// 编辑纺织品
const currentTex = ref(null)
const handleEdit = (row) => {
    currentTex.value = row.tex_id
    editTexDialog.value = true
}

// 关闭新增纺织品弹窗
const handleClose = () => {
    console.log('关闭新增纺织品弹窗')
    editTexDialog.value = false
    fetchData()
}

// 删除纺织品
const handleDelete = async (row) => {
    console.log('删除纺织品', row)
    await ElMessageBox.confirm('确认删除该纺织品?', '提示', {
        type: 'warning'
    })
    await deleteTextileApi(row.tex_id)
    ElMessage.success('删除成功')
    await fetchData()
}

// 初始化
onMounted(() => {
    fetchData()
})
</script>

<style scoped>
.tex-list {
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.title {
    font-size: 20px;
    font-weight: bold;
}

:deep(.el-table .cell) {
    white-space: nowrap;
}
</style>
