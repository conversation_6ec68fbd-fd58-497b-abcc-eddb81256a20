<!-- 设备状态监控 -->
<template>
  <div class="bg">
    <div>
      <SocketStatus keep-alive="true" />
      <SseStatus keep-alive="true" />
    </div>
    <el-row :gutter="10">
      <!-- 第一行设备 -->
      <el-col :span="2" v-for="dev in devs" :key="dev.dev_id">
        <devStateCard :dev_id="dev.dev_id" @click="showDeviceDetail(dev.dev_id)" />
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <!-- 第二行设备 -->
    </el-row>
  </div>

  <!-- 设备详情抽屉 -->
  <el-drawer v-model="drawerVisible" title="设备详情" direction="rtl" size="40%" :before-close="handleDrawerClose"
    :lock-scroll="false">
    <template #header>
      <div class="drawer-header">
        <span>{{ currentDevice?.name }} {{ currentDevice?.dev_id }}</span>
        <el-switch v-model="autoSwitch" active-text="自动切换" @change="handleAutoSwitchChange" />
      </div>
    </template>

    <!-- 设备事件列表 -->
    <div class="device-events">
      <h3>最近事件</h3>
      <el-timeline>
        <el-timeline-item v-for="(event, index) in recentEvents" :key="index" :type="getEventType(event.type)">
          <div class="event-content">
            <span class="event-type">[{{ event.type }}]</span>
            {{ event.name }}
            <span class="event-time">{{ formatTime(event.timestamp) }}</span>
          </div>
        </el-timeline-item>

      </el-timeline>
    </div>

    <!-- 属性表格组件 -->
    <div class="device-props">      
      <dev-real-table v-if="currentDevice" :dev_id="currentDevice.dev_id" />
    </div>
    <!-- 实时曲线图组件 -->
    <dev-real-charts v-if="currentDevice" :dev_id="currentDevice.dev_id" />
  </el-drawer>

</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useProductsStore } from '../../stores/modules/products'
import devStateCard from './components/StateCard.vue'

import devRealCharts from '../chart/devRealCharts.vue';
import DevRealTable from '../chart/DevRealTable.vue'
import SocketStatus from '@/views/chart/socketStatus.vue';
import SseStatus from '@/views/chart/sseStatus.vue';
import { useDataStore } from '../../stores/modules/data'


const pdStore = useProductsStore()
const dataStore = useDataStore()

const devs = ref([])
const drawerVisible = ref(false)
const currentDevice = ref({})
const autoSwitch = ref(false)
let autoSwitchTimer = null


// 显示设备详情
const showDeviceDetail = (dev_id) => {
  //console.log('showDeviceDetail', device)
  currentDevice.value = devs.value.find(dev => dev.dev_id === dev_id)
  drawerVisible.value = true
  if (autoSwitch.value) {
    resetAutoSwitch()
  }
}

// 处理抽屉关闭
const handleDrawerClose = () => {
  drawerVisible.value = false
  stopAutoSwitch()
}

// 自动切换开关处理
const handleAutoSwitchChange = (val) => {
  if (val) {
    startAutoSwitch()
  } else {
    stopAutoSwitch()
  }
}

// 开始自动切换
const startAutoSwitch = () => {
  autoSwitchTimer = setInterval(() => {
    const currentIndex = devs.value.findIndex(dev => dev.dev_id === currentDevice.value.dev_id)
    const nextIndex = (currentIndex + 1) % devs.value.length

    currentDevice.value = devs.value[nextIndex]
    console.log('自动切换到设备', currentDevice.value)
  }, 5000)
}

// 停止自动切换
const stopAutoSwitch = () => {
  if (autoSwitchTimer) {
    clearInterval(autoSwitchTimer)
    autoSwitchTimer = null
  }
}

// 重置自动切换计时器
const resetAutoSwitch = () => {
  stopAutoSwitch()
  startAutoSwitch()
}

// 获取当前设备的最近5条事件
const recentEvents = computed(() => {
  if (!currentDevice.value) return []
  // 监听 dataStore.devEvents 的变化
  const events = dataStore.devEvents[currentDevice.value.dev_id] || []

  return events.slice(0, 3)
})

// 获取事件类型对应的样式
const getEventType = (type) => {
  const typeMap = {
    '警告': 'warning',
    '故障': 'danger',
    '信息': 'info'
  }
  return typeMap[type] || 'info'
}

// 格式化时间戳
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
}

// 属性名称映射
const propNameMap = {
  temp: '温度',
  ph: 'PH值',
  level: '水位',
  speed: '转速'
}

// 属性单位映射
const propUnitMap = {
  temp: '℃',
  ph: '',
  level: '%',
  speed: 'rpm'
}

// 获取当前设备的最新属性数据
const latestProps = computed(() => {
  if (!currentDevice.value) return []

  const props = dataStore.devProps[currentDevice.value.dev_id] || []

  if (!props || !props.length) return []

  // 获取最新的属性数据（数组第一个元素）
  const latest = props[0]
  if (!latest) return []

  // 转换为表格数据格式
  return Object.entries(latest)
    .filter(([key]) => propNameMap[key]) // 只显示配置了名称映射的属性
    .map(([key, value]) => ({
      name: propNameMap[key] || key,
      value: typeof value === 'number' ? value.toFixed(1) : value,
      unit: propUnitMap[key] || ''
    }))
})

onMounted(async () => {
  devs.value = await pdStore.requestDevs()  
  //启动socket连接
  dataStore.connectSocket()
  //设置当前设备id
  currentDevice.value = devs.value[0]
  //启动sse连接
  //dataStore.connectSSE(['WSH25001'])
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoSwitch()
  //dataStore.disconnectSSE()
  console.log('卸载组件devState')
  //dataStore.disconnectSocket()
})
</script>

<style scoped>
.bg {
  height: 100%;
  width: 100%;
  padding: 15px;
  background-image: url('../../../public/images/bg.png');
  background-size: cover;
  background-position: center center;
}



.devicon {
  height: 100%;
}

.devicon img {
  width: 100%;
  object-fit: contain;
  /* 图片缩放方式：fill拉伸/缩小填充；contain保持宽高缩放填充；cover保持宽高比填充，超出部分裁剪 */
}

.devicon .danger {
  /* 图片遮罩 */
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(35, 19, 208, 0.2);
  z-index: 3000;
}

.devicon .info {
  display: none;
}

.devicon .success {
  display: none;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.device-events {
  padding: 20px;
}

.device-events h3 {
  margin-bottom: 20px;
  color: var(--el-text-color-primary);
}

.event-content {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.event-type {
  font-weight: bold;
  margin-right: 8px;
}

:deep(.el-timeline-item__node--large) {
  width: 14px;
  height: 14px;
}

:deep(.el-timeline-item__timestamp) {
  color: var(--el-text-color-secondary);
}

.device-props {
  padding: 20px;
  margin-top: 20px;
}

.device-props h3 {
  margin-bottom: 20px;
  color: var(--el-text-color-primary);
}

.prop-name {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.prop-value {
  color: var(--el-text-color-primary);
  font-size: 14px;
  font-weight: bold;
  margin-right: 4px;
}

.prop-unit {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

:deep(.el-table) {
  --el-table-border-color: var(--el-border-color-lighter);
  --el-table-bg-color: transparent;
  --el-table-tr-bg-color: transparent;
}

:deep(.el-table__cell) {
  background-color: transparent;
}
</style>