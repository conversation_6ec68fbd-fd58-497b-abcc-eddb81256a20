import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router/index'
import { localGet, getCsrfKey } from './index'

// 请求头，headers 信息
//axios.defaults.headers['X-Requested-With'] = 'XMLHttpRequest'


// 默认 post 请求，使用 application/json 形式;
// 'application/json'
// 'multipart/form-data'
//axios.defaults.headers.post['Content-Type'] = 'application/json'

const service = axios.create({
  baseURL: '/api',
  timeout: 5000,
  withCredentials: true,
})

service.interceptors.request.use(config => {
  //发生请求前做点什么  
  config.headers['X-CSRFToken'] = getCsrfKey('csrftoken')
  console.log('axios请求', config)
  return config
},
  error => {
    // do something with request error
    console.log('axios请求错误', error) // for debug
    return Promise.reject(error)
  }
)

// 请求拦截器，内部根据返回值，重新组装，统一管理。
service.interceptors.response.use(// 响应成功进入第1个函数，该函数的参数是响应对象
  response => {
    console.log('返回结果', response)
    const { data } = response
    // 后端API返回的结果，格式 code, msg, data
    if (data.code) {
      if (data.msg) {
        if (data.code !== 200){
          ElMessage.error(data.msg)
        }else{
          ElMessage.success(data.msg)
        }        
      }
    }
    if (data && data['url']) {
      router.push(data['url'])
    }
    return response.data
  },
  // 响应失败进入第2个函数，该函数的参数是错误对象
  async error => {
    // 如果响应码是 401 ，则请求获取新的 token
    // 响应拦截器中的 error 就是那个响应的错误对象
    console.log('返回错误', error)
    if (error.response && error.response.status === 401) {
      console.log("后端返回401")
      return Promise.reject(error)
    }
    if (error.response.data.msg) {
      ElMessage.error(error.response.data.msg)
    } else {
      ElMessage.error(error.message)
    }

    return Promise.reject(error)
  })

export default service