<template>
    <!-- 规则触发记录 -->
    <div class="history-container">
        <!-- 时间选择和操作按钮 -->
        <div class="toolbar">
            <div class="filter-group">
                <el-date-picker v-model="timeRange" type="datetimerange" range-separator="至" start-placeholder="开始时间"
                    end-placeholder="结束时间" :default-time="defaultTime" value-format="YYYY-MM-DD HH:mm:ss" />

            </div>
            <div class="buttons">
                <el-button type="primary" :icon="Search" @click="fetchHistoryData">查询</el-button>
                <el-button type="success" :icon="Download" @click="exportData">导出</el-button>
            </div>
        </div>

        <!-- 数据表格 -->
        <el-table :data="historyData" style="width: 100%" height="calc(100vh - 200px)" border v-loading="loading">
            <el-table-column prop="create_time" label="时间" width="180" fixed="left" />
            <!-- 规则id -->
            <el-table-column prop="rule_id" label="规则id" width="150" />
            <!-- 规则名称 -->
            <el-table-column prop="rule_name" label="规则名称" width="150" />
            <!-- 触发对象id -->
            <el-table-column prop="target_id" label="触发对象id" width="150" />
            <!-- 触发对象名称 -->
            <el-table-column prop="target_name" label="触发对象名称" width="150" />
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[20, 50, 100]"
                layout="total, sizes, prev, pager, next" :total="totalCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Download, TrendCharts } from '@element-plus/icons-vue'
import { getDevRuleHistoryApi } from '@/api/data'
import { useProductsStore } from '@/stores/modules/products'


const props = defineProps({
    dev_id: {
        type: String,
        required: false
    },
    rule_id: {
        type: String,
        required: false
    }
})

const pdStore = useProductsStore()
const timeRange = ref([])
const historyData = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(50)
const totalCount = ref(0)
const defaultTime = [
    new Date(2025, 1, 1, 0, 0, 0),
    new Date(2025, 1, 1, 23, 59, 59)
]

// 获取历史事件数据
const fetchHistoryData = async () => {
    if (!timeRange.value || !timeRange.value[0] || !timeRange.value[1]) {
        ElMessage.warning('请选择时间范围')
        return
    }

    loading.value = true
    try {
        const [startTime, endTime] = timeRange.value
        const params = {
            dev_id: props.dev_id,
            rule_id: props.rule_id,
            start_time: startTime,
            end_time: endTime,            
            page: currentPage.value,
            page_size: pageSize.value
        }

        const { data, total } = await getDevRuleHistoryApi(params)
        console.log('历史规则', data)
        historyData.value = data || []
        totalCount.value = total || data.length
    } catch (error) {
        ElMessage.error('获取历史规则失败')
        console.error('获取历史规则失败:', error)
    } finally {
        loading.value = false
    }
}


// 添加格式化函数
const formatObjectToString = (obj) => {
    if (!obj) return '';
    return Object.entries(obj)
        .map(([key, value]) => `${key}=${value}`)
        .join(', ');
}

// 过滤事件类型
const filterEventType = (value, row) => {
    return row.type === value
}

// 导出数据
const exportData = async () => {
    if (!historyData.value.length) {
        ElMessage.warning('暂无数据可导出')
        return
    }

    try {
        // 准备导出数据
        const exportData = historyData.value.map(item => ({
            '时间': item.create_time,
            '规则id': item.rule_id,
            '规则名称': item.rule_name,
            '触发对象id': item.trigger_id,
            '触发对象名称': item.trigger_name,
        }))

        // 创建工作簿
        const wb = XLSX.utils.book_new()
        const ws = XLSX.utils.json_to_sheet(exportData)
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

        // 导出文件
        const fileName = `设备事件历史_${formatTime(new Date())}.xlsx`
        XLSX.writeFile(wb, fileName)
    } catch (error) {
        ElMessage.error('导出失败')
        console.error('导出失败:', error)
    }
}

// 格式化时间
const formatTime = (time) => {
    return new Date(time).toLocaleString()
}

// 分页处理
const handleSizeChange = (val) => {
    pageSize.value = val
    fetchHistoryData()
}

const handleCurrentChange = (val) => {
    currentPage.value = val
    fetchHistoryData()
}

// 初始化
onMounted(async () => {
    // 设置默认时间范围为最近24小时
    const end = new Date()
    const start = new Date()
    start.setDate(start.getDate() - 1)

    const endTime = formatDate(end)
    const startTime = formatDate(start)
    timeRange.value = [startTime, endTime]

    // 获取历史数据
    await fetchHistoryData()
})

//转格式化时间,YYYY-MM-DD HH:mm:ss
const formatDate = (date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    const second = date.getSeconds().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}
</script>

<style scoped>
.history-container {
    padding-top: 10px;
    height: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;
}

.filter-group {
    display: flex;
    align-items: center;
}

.buttons {
    padding-left: 20px;
    display: flex;
    gap: 10px;
}

/* 固定表头 */
:deep(.el-table__header-wrapper) {
    position: sticky;
    top: 0;
    z-index: 1;
}

.pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
}

:deep(.el-table .cell pre) {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>