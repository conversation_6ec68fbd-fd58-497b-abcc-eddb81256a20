/* flex */
.flx-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .flx-justify-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .flx-align-center {
    display: flex;
    align-items: center;
  }
  
  /* clearfix */
  .clearfix::after {
    display: block;
    height: 0;
    overflow: hidden;
    clear: both;
    content: "";
  }
  
  /* 文字单行省略号 */
  .sle {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  /* 文字多行省略号 */
  .mle {
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  /* 文字多了自动換行 */
  .break-word {
    word-break: break-all;
    word-wrap: break-word;
  }