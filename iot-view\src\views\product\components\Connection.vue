<template>
    <div class="connection-container">
        <!-- 设备证书部分 -->
        <div class="section">
            <div class="section-header">
                <h3>设备证书</h3>
                <div class="header-desc">
                    每个设备拥有唯一的 AccessToken，设备连接云平台进行普通身份验证时使用 AccessToken，请妥善保管。
                </div>
            </div>
            <div class="button-group">
                <el-button @click="copyToken">
                    <el-icon>
                        <DocumentCopy />
                    </el-icon>复制 AccessToken
                </el-button>
                <el-button @click="copyToClipboard(projectKey)">
                    <el-icon>
                        <DocumentCopy />
                    </el-icon>复制 DEV_ID
                </el-button>
            </div>
        </div>

        <!-- MQTT接入点部分 -->
        <div class="section">
            <div class="section-header">
                <h3>设备端 MQTT 接入点</h3>
                <div class="header-desc">
                    MQTT 接入方式为设备和云平台提供双向连接，设备既可上报属性数据，也可接收云端的消息下发。
                </div>
            </div>
            <div class="connection-info">
                <div class="info-box">
                    <code>mqtt://{{ mqttHost }}:{{ mqttPort }}</code>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="label">MQTT 主机:</span>
                        <div class="value">
                            {{ mqttHost }}
                            <el-button link @click="copyToClipboard(mqttHost)">
                                <el-icon>
                                    <DocumentCopy />
                                </el-icon>
                            </el-button>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="label">MQTT 端口:</span>
                        <div class="value">
                            {{ mqttPort }}
                            <el-button link @click="copyToClipboard(mqttPort)">
                                <el-icon>
                                    <DocumentCopy />
                                </el-icon>
                            </el-button>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="label">Username:</span>
                        <div class="value">
                            <span class="placeholder">点击复制</span>
                            <el-button link @click="copyToken">
                                <el-icon>
                                    <DocumentCopy />
                                </el-icon>
                            </el-button>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="label">Password:</span>
                        <div class="value">
                            <span class="placeholder">点击复制</span>
                            <el-button link @click="copyToClipboard(projectKey)">
                                <el-icon>
                                    <DocumentCopy />
                                </el-icon>
                            </el-button>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="label">上报属性主题:</span>
                        <div class="value">
                            {{ telemetryTopic }}
                        </div>
                        <div v-if="isGateway" class="value">
                            子设备：{{ telemetryTopic2 }}
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="label">上报事件主题:</span>
                        <div class="value">
                            {{ eventTopic }}
                        </div>
                        <div v-if="isGateway" class="value">
                            子设备：{{ eventTopic2 }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- MQTTs TLS接入点部分 -->
        <div class="section">
            <div class="section-header">
                <h3>设备端 MQTTs TLS 接入点</h3>
                <div class="header-desc">
                    对于安全要求较高，并且有较多计算资源的设备，可以使用基于 SSL/TLS 的 MQTT 身份验证方式。仅面向专有区和私有区提供。

                </div>
            </div>
        </div>

        <!-- HTTP接入点部分 -->
        <div class="section">
            <div class="section-header">
                <h3>设备端 HTTP 接入点</h3>
                <div class="header-desc">
                    HTTP 接入方式较为简单快捷，适用于只上报数据，而不需要云端控制的设备。
                </div>

            </div>
            <div class="connection-info">
                <div class="info-box">
                    <code>mqtt://{{ mqttHost }}:{{ mqttPort }}</code>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useProductsStore } from '@/stores/modules/products';
import { DocumentCopy } from '@element-plus/icons-vue'
import { getDevInterfaceApi, getDevTokenApi } from '@/api/products';



const props = defineProps({
    dev_id: {
        type: String,
        required: true
    }
});

const pdStore = useProductsStore()

const accessToken = ref('');
const projectKey = ref('');
const mqttHost = ref('sh-3-mqtt.iot-api.com');
const mqttPort = ref('1883');
const isGateway = ref(false);
const telemetryTopic = ref('');
const eventTopic = ref('');
const telemetryTopic2 = ref('');
const eventTopic2 = ref('');

const fetchDeviceDetails = async () => {
    try {
        const data = await pdStore.getDevDetail(props.dev_id)
        projectKey.value = props.dev_id;        
        getTopic(data.connect_type)
    } catch (error) {
        ElMessage.error('获取设备信息失败');
    }
};

const getTopic = async (connect_type) => {
    if (connect_type === 'SUBDEV') {
        try {
            const Interface = await getDevInterfaceApi(props.dev_id)
            if (Interface?.gw_id) {
                telemetryTopic.value = `/sys/${Interface?.gw_id.toLowerCase()}/sub/telemetry`;
            eventTopic.value = `/sys/${Interface?.gw_id.toLowerCase()}/sub/event`;
            }else{
                ElMessage.error('设备未关联网关');
            }            
        } catch (error) {
            ElMessage.error('获取网关信息失败');
        }
        return
    } else {
        telemetryTopic.value = `/sys/${props.dev_id.toLowerCase()}/me/telemetry`;
        eventTopic.value = `/sys/${props.dev_id.toLowerCase()}/me/event`;

        if (connect_type === 'GATEWAY') {
            isGateway.value = true;
            telemetryTopic2.value = `/sys/${props.dev_id.toLowerCase()}/sub/telemetry`;
            eventTopic2.value = `/sys/${props.dev_id.toLowerCase()}/sub/event`;
        }
    }
}

const copyToken = async () => {
    try {
        const data = await getDevTokenApi(props.dev_id);
        copyToClipboard(data.token);
    } catch (error) {
        ElMessage.error('获取设备 Token 失败');
    }
}

const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
        ElMessage.success('复制成功');
    }).catch(() => {
        ElMessage.error('复制失败');
    });
};



onMounted(async () => {
    await fetchDeviceDetails();
});
</script>

<style scoped>
.connection-container {
    padding: 20px;
}

.section {
    margin-bottom: 30px;
    max-width: 800px;
}

.section-header {
    margin-bottom: 10px;
}

.section-header h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: bold;
}

.header-desc {
    color: #666;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.button-group {
    display: flex;
    gap: 10px;
}

.connection-info {
    border-radius: 4px;
}

.info-box {
    background: #2c3e50;
    color: #fff;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.info-box code {
    font-family: monospace;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.label {
    color: #666;
    font-size: 14px;
}

.value {
    display: flex;
    align-items: center;
    gap: 10px;
}

.placeholder {
    color: #999;
    font-style: italic;
}

:deep(.el-button--link) {
    padding: 0;
    height: auto;
}
</style>