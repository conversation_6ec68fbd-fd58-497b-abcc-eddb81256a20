import { ElMessage } from 'element-plus'
import { localGetToken } from './index'
import { ref } from 'vue';

class WebSocketClient {
    constructor() {
        this.ws = null
        this.url = '/ws/websocket/data_async/'  // 修改为新的WebSocket路径
        this.isConnected = false
        this.isConnectedRef = ref(false)    //方便外部监听
        this.reconnectAttempts = 0
        this.maxReconnectAttempts = 5
        this.reconnectInterval = 5000
        this.heartbeatInterval = 60000
        this.heartbeatTimer = null
        this.messageCallback = null
        this.isManualClosed = false  // 添加手动关闭标记
    }

    connect(callback) {
        if (this.ws && this.isConnected) {
            console.log('WebSocket已连接')
            return
        }

        this.messageCallback = callback
        this.isManualClosed = false // 重置手动关闭标记

        try {
            this.ws = new WebSocket(this.url)

            this.ws.onopen = () => {
                console.log('WebSocket连接成功')
                this.isConnected = true
                this.isConnectedRef.value = true
                this.reconnectAttempts = 0
                this.startHeartbeat() // 连接成功后立即开始心跳
                ElMessage.success('实时数据连接成功')
            }

            this.ws.onmessage = (event) => {
                const data = JSON.parse(event.data)
                console.log('收到消息:', data)

                // 处理心跳响应,数据中包含有heartbeat字段
                switch (data.type) {
                    case 'heartbeat':
                        console.log('心跳:', data.timestamp)
                        break
                    case 'device_data':
                        console.log('设备数据更新:', data.device_id, data.data)
                        if (this.messageCallback) {
                            this.messageCallback(data.data)
                        }
                        break
                    default:
                        console.log('未知消息类型:', data.type)
                }
            }

            this.ws.onclose = () => {
                console.log('WebSocket连接关闭')
                this.isConnected = false
                this.isConnectedRef.value = false
                this.stopHeartbeat()

                // 只有在非手动关闭的情况下才重连
                if (!this.isManualClosed) {
                    this.reconnect()
                }
            }

            this.ws.onerror = (error) => {
                console.error('WebSocket错误:', error)
                this.isConnected = false
                this.isConnectedRef.value = false
                ElMessage.error('连接发生错误')
            }

        } catch (error) {
            console.error('WebSocket连接失败:', error)
            this.reconnect()
        }
    }

    // 发送消息
    send(data) {
        if (!this.ws || !this.isConnected) {
            ElMessage.warning('WebSocket未连接')
            return
        }

        try {
            this.ws.send(JSON.stringify(data))            
        } catch (error) {
            console.error('发送消息失败:', error)
            ElMessage.error('发送消息失败')
        }
    }

    // 重连机制
    reconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('达到最大重连次数')
            ElMessage.error('连接失败，请刷新页面重试')
            return
        }

        this.reconnectAttempts++
        console.log(`尝试第 ${this.reconnectAttempts} 次重连`)

        setTimeout(() => {
            this.connect(this.messageCallback)
        }, this.reconnectInterval)
    }

    // 心跳检测
    startHeartbeat() {
        // 先清除可能存在的旧定时器
        this.stopHeartbeat()

        // 创建新的心跳定时器
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected) {
                // 发送心跳信号,时间戳
                this.send({ HeartBeat: Date.now() })                
            } else {
                this.stopHeartbeat() // 如果连接断开，停止心跳
            }
        }, this.heartbeatInterval)

        console.log('心跳检测已启动')
    }

    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer)
            this.heartbeatTimer = null
            console.log('心跳检测已停止')
        }
    }

    // 关闭连接
    close() {
        this.isManualClosed = true  // 设置手动关闭标记
        this.stopHeartbeat()
        if (this.ws) {
            this.ws.close()
            this.ws = null
            this.isConnected = false
            this.isConnectedRef.value = false
        }
    }
    getConnectedStatus() {
        return this.isConnectedRef.value;
    }
}
const socket = new WebSocketClient()
export default socket

