<template>
  <div class="login-body">
    <div class="left">
      <img src="/images/keqing.webp" alt="" width="320" class="leftimg">
    </div>
    <div class="login-container">
      <div class="head">
        <img class="logo" src="/images/qin.webp" />
        <div class="title">洗涤云服务平台</div>
      </div>
      <el-alert v-if="loginError" :title="loginError" type="error" effect="dark" show-icon :closable="false"
        class="mb-4" />
      <el-form :rules="formRules" :model="formData" ref="loginFormRef" label-position="top" @keyup.enter="handleLogin">
        <el-form-item label="账号" prop="username">
          <el-input v-model.trim="formData.username" placeholder="请输入账号" :prefix-icon="User" autocomplete="off" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model.trim="formData.password" type="password" placeholder="请输入密码" :prefix-icon="Lock"
            show-password autocomplete="off" />
        </el-form-item>

        <el-form-item>
          <div class="flex justify-between items-center mb-4">
            <el-checkbox v-model="rememberMe">下次自动登录</el-checkbox>
            <a href="#" class="text-primary">忘记密码？</a>
          </div>
          <el-button type="primary" class="w-full" :loading="loading" @click="handleLogin">
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { useAccount } from '../stores/modules/account'
import { useRouter } from 'vue-router'
import { User, Lock } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const accStore = useAccount()

// 表单引用
const loginFormRef = ref(null)
// 登录状态
const loading = ref(false)
const loginError = ref('')
const rememberMe = ref('')

// 表单数据
const formData = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { min: 3, message: '账号长度至少为3个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 4, message: '密码长度至少为4个字符', trigger: 'blur' }
  ]
}

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // 表单验证
    await loginFormRef.value.validate()

    // 开始登录
    loading.value = true
    loginError.value = ''
    formData.remember=rememberMe.value

    // 调用登录接口
    await accStore.login(formData)

    ElMessage.success('登录成功')
    router.push('/')

  } catch (error) {
    console.error('登录失败:', error)
    if (error.response?.status === 401) {
      loginError.value = '用户名或密码错误'
    } else {
      loginError.value = error.message || '登录失败，请稍后重试'
    }
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  // 获取CSRF Token
  accStore.getToken()
})
</script>

<style scoped>
.login-body {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  width: 100%;
  background-image: url('/images/data.jpg');
}

.login-container {
  width: 360px;
  height: 420px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 21px 41px 0px rgba(0, 0, 0, 0.2);
  padding: 25px;
  margin-left: 400px;

}

.leftimg {
  border-radius: 10px;
}

.head {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0 20px 0;
}

.head img {
  width: 100px;
  height: 100px;
  margin-right: 20px;
}

.head .title {
  font-size: 28px;
  color: #1BAEAE;
  font-weight: bold;
}

.head .tips {
  font-size: 12px;
  color: #999;
}

@media (max-width:800px) {
  .left {
    display: none;
  }

  .login-container {
    margin-left: 0px;
  }

}

.mb-4 {
  margin-bottom: 1rem;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.text-primary {
  color: #409eff;
  text-decoration: none;
}

.text-primary:hover {
  text-decoration: underline;
}
</style>