<!-- 酒店表格 -->
<template>
    <div class="hotel-list">
        <!-- 标题栏 -->
        <div class="header">
            <div class="title">客户信息</div>            
        </div>
        <el-divider style="margin: 20px 0px 10px 0px;" />
        <div class="section-header">                
            <el-button type="primary" @click="handleAdd">新建客户</el-button>
        </div>
        <!-- 规则列表 -->
        <el-table v-loading="loading" :data="hotelList" border style="width: 100%">
            <el-table-column prop="hotel_id" label="酒店ID" min-width="200" />
            <el-table-column prop="name" label="酒店名称" min-width="200" />
            <el-table-column prop="address" label="酒店地址" min-width="200" />
            <el-table-column prop="status" label="状态" min-width="100">
                <template #default="{ row }">
                    <el-tag :type="getStatusTag(row.status)">{{ row.status===0?'停用':'正常' }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="create_time" label="创建时间" min-width="200" sortable />
            <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                    <el-button type="primary" link @click="handleView(row)">查看详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 新建酒店弹窗 -->
        <el-dialog v-model="hotelEditDialog" title="新建酒店" width="50%">
            <HotelEdit />
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTextileStore } from '@/stores/modules/textile';
import { getHotelsApi } from '@/api/textile'
import { useRouter } from 'vue-router'
import HotelEdit from './hotelEdit.vue'

const texStore = useTextileStore()
const router = useRouter()
// 列表数据

const hotelList = ref([])
const loading = ref(false)


// 获取酒店列表
const fetchData = async () => {
    loading.value = true
    try {
        hotelList.value = await texStore.requestHotels()
        console.log('hotelList', hotelList.value)
    } catch (error) {
        ElMessage.error('获取酒店列表失败')
    } finally {
        loading.value = false
    }
}

// 获取状态标签
const getStatusTag = (status) => {
    //0和1
    if (status === 0) {
        return 'danger'
    } else {
        return 'success'
    }
}

// 查看详情
const handleView = (row) => {
    router.push(`/hotel/${row.hotel_id}`)
}

// 新建酒店弹窗
const hotelEditDialog = ref(false)
// 新增酒店
const handleAdd = () => {
    hotelEditDialog.value = true
}


// 初始化
onMounted(() => {
    fetchData()
})
</script>

<style scoped>
.hotel-list {
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.title {
    font-size: 20px;
    font-weight: bold;
}
.section-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 15px;
}

:deep(.el-table .cell) {
    white-space: nowrap;
}
</style>
