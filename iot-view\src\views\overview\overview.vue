<!-- 首页-概览页面，有设备状态饼图、 -->
<template>
    <div class="overview">
        <el-row :gutter="10">
            <el-col :span="20">
                <h1>概览</h1>
            </el-col>
            <el-col :span="4">
                <!-- WebSocket连接状态 -->
                <div class="socket-status">
                    <SocketStatus keep-alive="true" />
                </div>
            </el-col>
        </el-row>
        <el-row :gutter="10">
            <!-- 第一行,每行共24列 -->
            <el-col :span="6">
                <DevStatusPie :dev_ids="dev_ids" />
            </el-col>
            <el-col :span="18">
                <el-row :gutter="10">
                    <div class="toolbar">
                    <el-date-picker v-model="timeRange" type="datetimerange" range-separator="至"
                        start-placeholder="开始时间" end-placeholder="结束时间" :default-time="defaultTime"
                        value-format="YYYY-MM-DD HH:mm:ss" />
                </div>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <DevsProductionBar :dev_ids="wsh_ids" :chart_series="dev_infos" :time_range="timeRange" chart_name="水洗机每小时产量" />
                    </el-col>
                    <el-col :span="12"> 
                        <DevsProductionBar :dev_ids="fod_ids" :chart_series="fod_infos" :time_range="timeRange" chart_name="折叠机每小时产量" />
                    </el-col>
                </el-row>
            </el-col>
            
        </el-row>
        <el-row :gutter="10">
            <el-col :span="6">
                
            </el-col>
            <el-col :span="18">
                <el-row :gutter="10">
                    <div class="toolbar">
                    <el-date-picker v-model="timeRange" type="datetimerange" range-separator="至"
                        start-placeholder="开始时间" end-placeholder="结束时间" :default-time="defaultTime"
                        value-format="YYYY-MM-DD HH:mm:ss" />
                </div>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <DevsProductionBar :dev_ids="wsh_ids" :chart_series="dev_infos" :time_range="timeRange" chart_name="水洗机每天产量" unit="day" />
                    </el-col>
                    <el-col :span="12"> 
                        <DevsProductionBar :dev_ids="fod_ids" :chart_series="fod_infos" :time_range="timeRange" chart_name="折叠机每天产量" unit="day" />
                    </el-col>
                </el-row>
            </el-col>
        </el-row>
        <el-row :gutter="10">
            <el-col :span="12">
                <div class="production-chart">
                    
                </div>
            </el-col>
            <el-col :span="12">
                <div class="production-chart">
                    
                </div>
            </el-col>
        </el-row>
        <el-row>

        </el-row>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import DevStatusPie from '@/views/chart/devStatusPie.vue';
import SocketStatus from '@/views/chart/socketStatus.vue';
import DevsProductionBar from '@/views/chart/devsProductionBar.vue';

const timeRange = ref([]);

const defaultTime = [
    new Date(2025, 1, 1, 0, 0, 0),
    new Date(2025, 1, 1, 23, 59, 59)
]
const dev_infos = ref([
    { name: '水洗机1号', identifier: 'WSH25001' },
    { name: '水洗机2号', identifier: 'WSH25002' },    
])

const dev_ids=['WSH25001','WSH25002','WSH25003','FOD25001','FOD25002','DRY25001','DRY25002']
const wsh_ids=['WSH25001','WSH25002']
const fod_ids=['FOD25001','FOD25002']

const fod_infos = ref([
    { name: '折叠机1号', identifier: 'FOD25001' },
    { name: '折叠机2号', identifier: 'FOD25002' },
])

onMounted(() => {
    const end = new Date()
    const start = new Date()
    start.setDate(start.getDate() - 1)

    const endTime = formatDate(end)
    const startTime = formatDate(start)
    timeRange.value = [startTime, endTime]
});

//转格式化时间,YYYY-MM-DD HH:mm:ss
const formatDate = (date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    const second = date.getSeconds().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}

</script>

<style scoped>
.overview {
    height: 100%;
    padding: 10px;
}

.toolbar {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;
    width: 100%;
}
</style>
