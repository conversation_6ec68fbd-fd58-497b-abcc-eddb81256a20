import request from '@/utils/request'

//获取每小时产量
export function getDevsHourlyProductionApi(data) {
    return request({
        url: '/data/getDevsHourlyProduction/',
        method: 'get',
        params: data
    })
}

//获取每天产量
export function getDevsDailyProductionApi(data) {
    return request({
        url: '/data/getDevsDailyProduction/',
        method: 'get',
        params: data
    })
}
//获取酒店每天生产数据
export function getHotelDailyProductionApi(data) {
    return request({
        url: '/data/getHotelDailyProduction/',
        method: 'get',
        params: data
    })
}

//获取布草每天洗涤量
export function getTexsDailyProductionApi(data) {
    //参数tex_ids,dev_ids,start_time,end_time
    return request({
        url: '/aggregate/getTexsDailyProduction/',
        method: 'get',
        params: data
    })
}
