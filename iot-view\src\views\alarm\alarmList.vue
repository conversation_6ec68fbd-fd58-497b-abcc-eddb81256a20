<template>
    <div class="rule-list">
        <!-- 标题栏 -->
        <div class="header">
            <div class="title" v-if="!pro_id && !dev_id">报警规则</div>
            <el-button type="primary" @click="handleAdd">创建报警</el-button>
        </div>

        <!-- 报警列表 -->
         
        <el-table v-loading="loading" :data="ruleList" border style="width: 100%">
            <el-table-column prop="name" label="报警名称" min-width="200" />
            <el-table-column prop="type" label="事件类型" width="100">
                <template #default="{ row }">                    
                    {{ getEventTypeLabel(row.type) }}                    
                </template>
            </el-table-column>            
            <el-table-column prop="level" label="事件等级" width="100">
                <template #default="{ row }">
                    <el-tag :type="getEventLevelTag(row.level)">
                        {{ getEventLevelLabel(row.level) }}
                    </el-tag>
                </template>
            </el-table-column>

            <el-table-column prop="target_type" label="规则对象类型" min-width="120" :filters="[
                { text: '产品报警', value: 'PRODUCT' },
                { text: '设备报警', value: 'DEVICE' }
            ]" :filter-method="filterTargetType" filter-placement="bottom">
                <template #default="{ row }">                    
                    {{ getRuleTargetTypeLabel(row.target_type) }}                    
                </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" min-width="100" :filters="[
                { text: '启用', value: 1 },
                { text: '禁用', value: 0 }
            ]" :filter-method="filterStatus" filter-placement="bottom">
                <template #default="{ row }">
                    <el-switch v-model="row.status" :active-value="1" :inactive-value="0"
                        @change="handleStatusChange(row)" />
                </template>
            </el-table-column>

            <el-table-column prop="update_time" label="更新时间" min-width="200" sortable />

            <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                    <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
                    <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getDevAlarmsApi, updateAlarmStatusApi, deleteAlarmApi } from '@/api/products'

const router = useRouter()

//可以接收props
const props = defineProps({
    pro_id: {
        type: String,
        default: ''
    },
    dev_id: {
        type: String,
        default: ''
    }
})

// 列表数据
const ruleList = ref([])
const loading = ref(false)

// 过滤方法
const filterTargetType = (value, row) => {
    return row.target_type === value
}

const filterStatus = (value, row) => {
    return row.status === value
}

// 获取规则列表
const fetchRuleList = async () => {
    loading.value = true
    try {
        ruleList.value = await getDevAlarmsApi(props.pro_id,props.dev_id)
        console.log('alarmList', ruleList.value)
    } catch (error) {
        ElMessage.error('获取报警列表失败')
    } finally {
        loading.value = false
    }
}

// 规则类型标签
const getRuleTargetTypeTag = (type) => {
    const map = {
        'PRODUCT': 'success',
        'DEVICE': 'info',
    }
    return map[type]
}

const getRuleTargetTypeLabel = (type) => {
    const map = {
        'PRODUCT': '产品报警',
        'DEVICE': '设备报警',
    }
    return map[type]
}

// 获取事件类型显示文本
const getEventTypeLabel = (type) => {
    const map = {
        'ALARM': '故障告警',
        'STATUS': '运行状态',
        'EVENT': '设备事件'
    }
    return map[type] || type
}
// 获取事件等级标签样式
const getEventLevelTag = (level) => {
    const map = {
        'DEBUG': 'info',
        'INFO': 'info',
        'WARN': 'warning',
        'ERROR': 'danger',
        'FATAL': 'danger'
    }
    return map[level] || 'info'
}
// 获取事件等级显示文本
const getEventLevelLabel = (level) => {
    const map = {
        'DEBUG': '调试',
        'INFO': '提示',
        'WARN': '告警',
        'ERROR': '错误',
        'FATAL': '严重'
    }
    return map[level] || level
}

// 创建规则
const handleAdd = () => {
    router.push('/alarm_add')
}

// 状态变更
const handleStatusChange = async (row) => {
    try {
        // TODO: 调用状态更新API
        await updateAlarmStatusApi(row.id, row.status)
        ElMessage.success('状态更新成功')
    } catch (error) {
        row.status = row.status === 1 ? 0 : 1 // 恢复状态
        ElMessage.error('状态更新失败')
    }
}

// 编辑报警
const handleEdit = (row) => {
    router.push(`/alarm/${row.id}`)
}

// 删除报警
const handleDelete = (row) => {
    console.log('删除报警', row)
    //弹窗确认
    ElMessageBox.confirm('确定要删除报警吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        // TODO: 调用删除API
        await deleteAlarmApi(row.id)
        ElMessage.success('删除报警成功')
        fetchRuleList()
    })
}


// 初始化
onMounted(() => {
    fetchRuleList()
})
</script>

<style scoped>
.rule-list {
    padding: 0px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.title {
    font-size: 20px;
    font-weight: bold;
}

:deep(.el-table .cell) {
    white-space: nowrap;
}
</style>
