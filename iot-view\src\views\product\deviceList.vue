//所有设备的列表，
<template>
    <div class="container">
        <h2 class="page-title">设备</h2>
        <el-row>
            <el-col :span="6">
                <el-select v-model="filter.type" placeholder="选择设备类型">
                    <el-option v-for="type in deviceTypes" :key="type.value" :label="type.label" :value="type.value" />
                </el-select>
            </el-col>
            <el-col :span="6">
                <el-input v-model="filter.query" placeholder="输入设备名称或类型进行查询" />
            </el-col>
            <el-col :span="6">
                <el-button type="primary" @click="search">查询</el-button>
            </el-col>
        </el-row>
        <el-divider />
        <el-row>
            <el-col :span="4" v-for="device in filteredDevices" :key="device.dev_id">                
                <router-link :to="`/device/${device.dev_id}`"><!-- 使用路由的parmars传参 -->
                    <el-card class="device-card" shadow="hover">
                        <div class="device-header">
                            <img class="device-icon" src="/images/sailstar.png" alt="设备图标" />
                            <div class="device-info">
                                <div class="device-name">{{ device.name }}</div>
                                <div class="device-type">{{ device.type }}</div>
                            </div>
                        </div>
                        <div class="device-footer">
                            <div class="device-status">{{ devStatus[device.dev_id]?.name||'--' }}</div>      
                            <div class="device-status">{{devStatus[device.dev_id]?.create_time||''}}</div>                                             
                        </div>
                    </el-card>
                </router-link>
            </el-col>
        </el-row>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElSelect, ElOption, ElInput, ElButton, ElRow, ElCol, ElCard } from 'element-plus';
import { useProductsStore } from '@/stores/modules/products';
import { useDataStore } from '@/stores/modules/data';

const pdStore = useProductsStore();
const dataStore = useDataStore();

//TODO: 新建设备
const devStatus = ref({});

onMounted(async () => {
    //获取设备列表
    const devs=await pdStore.requestDevs();

    //根据设备列表生成设备类型，去重    
    pdStore.devs.forEach(device => {
        if (!deviceTypes.value.some(type => type.value === device.type)) {
            deviceTypes.value.push({ value: device.type, label: device.type });
        }
    });
    filteredDevices.value = pdStore.devs;
    //获取设备状态
    const dev_ids= devs.map(item=>item.dev_id)
    const state=await dataStore.requestDevsState(dev_ids);
    state.forEach(item=>{
        //把日期改为只显示MM-DD HH:MM
        item.end_time=item.end_time.split(' ')[0].split('-').slice(1,3).join('-')+' '+item.end_time.split(' ')[1].split(':').slice(0,2).join(':');
        devStatus.value[item.dev_id]=item;
    });
});

// 设备类型选项
const deviceTypes = ref([{ value: '', label: '全部' }]);

// 筛选条件
const filter = ref({
    type: '',    
    query: '',
});

// 计算属性：根据筛选条件过滤设备列表
const filteredDevices = computed(() => {
    return pdStore.devs.filter(device => {
        const typeMatch = !filter.value.type || device.type === filter.value.type;        
        const queryMatch = !filter.value.query ||
            device.name.includes(filter.value.query) ||
            device.type.includes(filter.value.query);
        return typeMatch && queryMatch;
    });
});

//获取设备的状态，这里使用批量获取


// 查询方法
const search = () => {
    // 这里可以添加查询逻辑，例如向服务器发送请求获取过滤后的设备列表
    // 目前示例中直接使用计算属性 filteredDevices 进行过滤
};
</script>

<style scoped>
.container {
    padding: 20px;
}
.device-card {
    margin-bottom: 20px;
    margin: 10px;   
    border-radius: 5px;
}

.device-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.device-icon {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.device-info {
    display: flex;
    flex-direction: column;
}

.device-name {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.device-type {
    font-size: 12px;
    color: var(--el-color-secondary);
    margin-top: 5px;
}

.device-footer {    
    justify-content: space-between;
    align-items: center;
    display: flex;
}

.device-status {
    font-size: 12px;
    color: #333;
}

.device-alarm {
    font-size: 12px;
    color: #ff0000;
}
.page-title {  
  margin: 20px 15px;  
}
</style>