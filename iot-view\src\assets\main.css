@import './base.css';


* {
  margin: 0;
  padding: 0;
  list-style-type: none;
  box-sizing: border-box;
  outline: none;
}

html {
  margin: 0;
  padding: 0;
  height: 100%;
}

body {
  font-family: Arial, Helvetica, sans-serif;
  line-height: 1.2em;
  background-color: #f1f1f1;
  margin: 0;
  padding: 0;
  overflow: hidden;
  height: 100%;
}

a {
  color: #343440;
  text-decoration: none;
  box-sizing: border-box;
}

.clearfix {
  &::after {
    content: "";
    display: table;
    height: 0;
    line-height: 0;
    visibility: hidden;
    clear: both;
  }
}


.iconfont {
  font-size: 20px!important;
  color: #5cd9e8;
}




/*文章一行显示，多余省略号显示
*/
.title-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bg-color-black {
  background-color: rgba(19, 25, 47, 0.6);
}

#app {
  /*max-width: 1280px;*/
  margin: 2 auto;  
  font-weight: normal;
  height: 100%;
}


a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
}
