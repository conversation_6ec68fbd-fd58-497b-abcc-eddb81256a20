import request from '@/utils/request'


//请求设备的最新数据,需设备id列表
export function getDevProcessDetailLatestApi(dev_id) {
  return request({
    url: '/data/getDevProcessDetailLatest/',
    method: 'get',
    params: {
      dev_id: dev_id,
    }
  })
}

//请求设备的历史数据,需设备id列表
export function getDevProcessDetailHistoryApi(data) {
  return request({
    url: '/data/getDevProcessDetailHistory/',
    method: 'get',
    params: data
  })
}

//获取设备的事件
export function getDevEventApi(data) {
  return request({
    url: '/data/getDevEvent/',
    method: 'get',
    params: {
      dev_id: data.dev_id,
    }
  })
}

//获取设备的历史事件
export function getDevEventHistoryApi(data) {
  return request({
    url: '/data/getDevEventHistory/',
    method: 'get',
    params: data
  })
}

//获取设备的历史规则
export function getDevRuleHistoryApi(data) {
  return request({
    url: '/data/getDevRuleHistory/',
    method: 'get',
    params: data
  })
}

//获取设备状态时间统计
export function getDevsStatusHistoryApi(data) {
  return request({
    url: '/data/getDevsStatusHistory/',
    method: 'get',
    params: data
  })
}

//获取设备最新状态
export function getDevsStatusApi(dev_ids) {
  return request({
    url: '/data/getDevsStatusLatest/',
    method: 'get',
    params: {
      dev_ids: dev_ids,
    }
  })
}
//获取设备最新状态
export function getDevsStatusCurrentApi(dev_ids) {
  return request({
    url: '/data/getDevsStatusCurrent/',
    method: 'get',
    params: {
      dev_ids: dev_ids,
    }
  })
}

//获取设备最后在线时间
export function getDevsLastOnlineTimeApi(data) {
  //参数:dev_ids
  return request({
    url: '/data/getDevsLastOnlineTime/',
    method: 'get',
    params: data
  })
}

//插入设备过程数据
export function createDevProcessDetailApi(data) {
  //转formdata
  const formData = new FormData()
  for (const key in data) {
    formData.append(key, data[key])
  }
  return request({
    url: '/data/createDevProcessDetail/',
    method: 'post',
    data: formData
  })
}
