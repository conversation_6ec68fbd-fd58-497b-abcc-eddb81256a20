<!-- 用户列表 -->
<template>
    <div class="user-list">
        <!-- 标题栏 -->
        <div class="header">
            <div class="title">用户列表</div>
            <el-button type="primary" @click="handleAdd">新增用户</el-button>
        </div>

        <!-- 用户列表 -->
        <el-table v-loading="loading" :data="userList" border style="width: 100%">            
            <el-table-column prop="username" label="账户" min-width="100" />
            <el-table-column prop="ch_name" label="姓名" min-width="120" />
            <el-table-column prop="phone" label="手机号" min-width="150" />
            <el-table-column prop="roles" label="权限" min-width="300" />
            <el-table-column prop="is_active" label="状态" min-width="150">
                <template #default="{ row }">
                    <el-switch v-model="row.is_active" :active-value="1" :inactive-value="0"
                        @change="handleStatusChange(row)" />
                </template>
            </el-table-column>

            <el-table-column prop="department" label="部门" min-width="200" />            
            <el-table-column prop="update_time" label="更新时间" min-width="200" sortable />
            <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                    <el-button-group>
                        <el-button size="small" :icon="Edit" @click="handleEdit(row)"></el-button>
                        <el-button size="small" :icon="Delete" type="danger" @click="handleDelete(row)"></el-button>
                    </el-button-group>
                </template>
            </el-table-column>
        </el-table>
        <!-- 新增用户弹窗 -->
        <el-dialog v-model="editUserDialog" title="新增用户" width="40%">
            <UserEdit :user="currentUser" @close="handleClose" />
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Delete } from '@element-plus/icons-vue'
import { getUsersApi,deleteUserApi,setUserStatusApi,setUserPasswordApi } from '@/api/user'
import UserEdit from './userEdit.vue'


// 列表数据
const userList = ref([])
const loading = ref(false)

// 获取用户列表
const fetchData = async () => {
    loading.value = true    

    try {
        userList.value = await getUsersApi()        
    } catch (error) {
        ElMessage.error('获取用户列表失败')
    } finally {
        loading.value = false
    }
}
// 状态变更
const handleStatusChange = async (row) => {
    try {
        // TODO: 调用状态更新API
        await setUserStatusApi(row.id, row.is_active)        
    } catch (error) {
        row.is_active = row.is_active === 1 ? 0 : 1 // 恢复状态
        ElMessage.error('状态更新失败')
    }
}
// 新增用户弹窗
const editUserDialog = ref(false)
// 新增用户
const handleAdd = () => {
    currentUser.value = null
    editUserDialog.value = true
}

// 编辑用户
const currentUser = ref(null)
const handleEdit = (row) => {
    currentUser.value = row
    editUserDialog.value = true
}

// 关闭新增用户弹窗
const handleClose = () => {
    console.log('关闭新增用户弹窗')
    editUserDialog.value = false
    fetchData()
}

// 删除用户
const handleDelete = async (row) => {
    console.log('删除用户', row)
    await ElMessageBox.confirm('确认删除该用户?', '提示', {
        type: 'warning'
    })
    await deleteUserApi(row.user_id)
    ElMessage.success('删除成功')
    await fetchData()
}

// 初始化
onMounted(() => {
    fetchData()
})
</script>

<style scoped>
.user-list {
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.title {
    font-size: 20px;
    font-weight: bold;
}

:deep(.el-table .cell) {
    white-space: nowrap;
}
</style>
