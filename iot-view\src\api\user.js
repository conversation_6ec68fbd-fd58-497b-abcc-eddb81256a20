import request from '@/utils/request'

export function loginApi(data) {
  console.log('登录API', data)
  const formData = new FormData()
  formData.append('username', data.username)
  formData.append('password', data.password)
  formData.append('remember', data.remember)

  return request({
    url: '/users/login/',
    method: 'post',
    data: formData
  })
}

export function getUserInfoApi() {
  return request({
    url: '/users/info/',
    method: 'get'
  })
}

export function logoutApi() {
  return request({
    url: '/users/logout/',
    method: 'get'
  })
}

export function getTokenApi() {
  return request({
    url: '/users/gettoken/',
    method: 'get'
  })
}

export function changePasswordApi(data) {
  const formData = new FormData()
  formData.append('password', data.password)
  formData.append('new_password', data.new_password)  
  return request({
    url: '/users/changePassword/',
    method: 'post',
    data: formData
  })
}

export function getUsersApi() {
  return request({
    url: '/users/getUsers/',
    method: 'get'    
  })
}

export function addUserApi(data) {
  const formData = new FormData()
  for (const key in data) {
    formData.append(key, data[key])
  }
  return request({
    url: '/users/addUser/',
    method: 'post',
    data: formData
  })
}

export function updateUserApi(data) {
  const formData = new FormData()
  for (const key in data) {
      formData.append(key, data[key])    
  }

  return request({
    url: '/users/updateUser/',
    method: 'post',
    data: formData
  })
}

export function deleteUserApi(user_id) {
  const formData = new FormData()
  formData.append('user_id', user_id)
  return request({
    url: '/users/deleteUser/',
    method: 'post',
    data: formData
  })
}

export function getUserRolesApi() {
  return request({
    url: '/users/getGroups/',
    method: 'get'
  })
}

export function getUserPermissionsApi() {
  return request({
    url: '/users/getPermissions/',
    method: 'get'
  })
}

export function setUserStatusApi(user_id,is_active) {
  console.log('设置用户状态', user_id,is_active)
  const formData = new FormData()
  formData.append('user_id', user_id)
  formData.append('is_active', is_active)
  return request({
    url: '/users/setUserStatus/',
    method: 'post',
    data: formData
  })
}

export function setUserPasswordApi(user_id,password){
  const formData = new FormData()
  formData.append('user_id', user_id)
  formData.append('password', password)
  return request({
    url: '/users/setUserPassword/',
    method: 'post',
    data: formData
  })
}