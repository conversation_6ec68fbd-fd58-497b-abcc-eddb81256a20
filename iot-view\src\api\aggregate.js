import request from '@/utils/request'

//获取布草洗涤量
export function getTexsProductionApi(data) {
    //参数tex_ids,dev_ids,start_time,end_time，time_type
    return request({
        url: '/aggregate/getTexsProduction/',
        method: 'get',
        params: data
    })
}

//获取设备洗涤量
export function getDevsProductionApi(data) {
    //参数dev_ids,start_time,end_time，time_type
    return request({
        url: '/aggregate/getDevsProduction/',
        method: 'get',
        params: data
    })
}

//获取酒店洗涤量
export function getHotelsProductionApi(data) {
     //参数hotel_ids,dev_ids,start_time,end_time，time_type
    return request({
        url: '/aggregate/getHotelsProduction/',
        method: 'get',
        params: data
    })
}

//获取所有酒店的洗涤量
export function getAllHotelsProductionApi(data) {
    return request({
        url: '/aggregate/getAllHotelsProduction/',
        method: 'get',
        params: data
    })
}

//获取设备洗涤流程
export function getDevsProcessApi(data) {
    //参数dev_ids,start_time,end_time
    return request({
      url: '/aggregate/getDevsProcess/',
      method: 'get',
      params:data
    })  
}

//获取设备洗涤流程详情
export function getDevsProcessDetailApi(data) {
    //参数process_ids
    return request({
      url: '/aggregate/getDevsProcessDetail/',
      method: 'get',
      params:data
    })  
  }