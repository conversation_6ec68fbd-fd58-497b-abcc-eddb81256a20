<template>
    <div class="rule-list">
        <!-- 标题栏 -->
        <div class="header">
            <div class="title" v-if="!pro_id && !dev_id">消息规则</div>
            <el-button type="primary" @click="handleAdd">创建规则</el-button>
        </div>

        <!-- 规则列表 -->
        <el-table v-loading="loading" :data="ruleList" border style="width: 100%">
            <el-table-column prop="name" label="规则名称" min-width="200" />

            <el-table-column prop="target_type" label="规则对象类型" min-width="120" :filters="[
                { text: '产品规则', value: 'PRODUCT' },
                { text: '设备规则', value: 'DEVICE' }
            ]" :filter-method="filterTargetType" filter-placement="bottom">
                <template #default="{ row }">
                    <el-tag :type="getRuleTargetTypeTag(row.target_type)">
                        {{ getRuleTargetTypeLabel(row.target_type) }}
                    </el-tag>
                </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" min-width="100" :filters="[
                { text: '启用', value: 1 },
                { text: '禁用', value: 0 }
            ]" :filter-method="filterStatus" filter-placement="bottom">
                <template #default="{ row }">
                    <el-switch v-model="row.status" :active-value="1" :inactive-value="0"
                        @change="handleStatusChange(row)" />
                </template>
            </el-table-column>

            <el-table-column prop="update_time" label="更新时间" min-width="200" sortable />

            <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                    <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getRulesApi } from '@/api/products'

const router = useRouter()
//可以接收props
const props = defineProps({
    pro_id: {
        type: String,
        default: ''
    },
    dev_id: {
        type: String,
        default: ''
    }
})

// 列表数据
const ruleList = ref([])
const loading = ref(false)

// 过滤方法
const filterTargetType = (value, row) => {
    return row.target_type === value
}

const filterStatus = (value, row) => {
    return row.status === value
}

// 获取规则列表
const fetchRuleList = async () => {
    loading.value = true
    try {
        ruleList.value = await getRulesApi({pro_id:props.pro_id,dev_id:props.dev_id})
        console.log('ruleList', ruleList.value)
    } catch (error) {
        ElMessage.error('获取规则列表失败')
    } finally {
        loading.value = false
    }
}

// 规则类型标签
const getRuleTargetTypeTag = (type) => {
    const map = {
        'PRODUCT': 'success',
        'DEVICE': 'info',
    }
    return map[type]
}

const getRuleTargetTypeLabel = (type) => {
    const map = {
        'PRODUCT': '产品规则',
        'DEVICE': '设备规则',
    }
    return map[type]
}

// 创建规则
const handleAdd = () => {
    router.push('/rule_add')
}

// 状态变更
const handleStatusChange = async (row) => {
    try {
        // TODO: 调用状态更新API
        // await updateRuleStatus(row.id, row.status)
        ElMessage.success('状态更新成功')
    } catch (error) {
        row.status = row.status === 1 ? 0 : 1 // 恢复状态
        ElMessage.error('状态更新失败')
    }
}

// 编辑规则
const handleEdit = (row) => {
    router.push(`/rule/${row.id}`)
}

// 初始化
onMounted(() => {
    fetchRuleList()
})
</script>

<style scoped>
.rule-list {
    padding: 0px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.title {
    font-size: 20px;
    font-weight: bold;
}

:deep(.el-table .cell) {
    white-space: nowrap;
}
</style>
