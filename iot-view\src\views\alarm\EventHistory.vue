<template>
    <div class="history-container">
        <!-- 时间选择和操作按钮 -->
        <div class="toolbar">
            <div class="filter-group">
                <el-date-picker v-model="timeRange" type="datetimerange" range-separator="至" start-placeholder="开始时间"
                    end-placeholder="结束时间" :default-time="defaultTime" value-format="YYYY-MM-DD HH:mm:ss" />
                <el-select v-model="eventLevel" placeholder="事件等级" clearable style="width: 120px; margin-left: 10px;">
                    <el-option label="全部" value="" />
                    <el-option label="严重" value="FATAL" />
                    <el-option label="故障" value="ERROR" />
                    <el-option label="警告" value="WARNING" />
                    <el-option label="故障" value="ERROR" />
                    <el-option label="提示" value="INFO" />
                    <el-option label="调试" value="DEBUG" />
                </el-select>
            </div>
            <div class="buttons">
                <el-button type="primary" :icon="Search" @click="fetchHistoryData">查询</el-button>
                <!-- 查看数据曲线 -->
                <el-button type="primary" :icon="TrendCharts" @click="showHistoryChart">查看曲线</el-button>
                <el-button type="success" :icon="Download" @click="exportData">导出</el-button>
            </div>
        </div>

        <!-- 数据表格 -->
        <el-table :data="historyData" style="width: 100%" height="calc(100vh - 200px)" border v-loading="loading">
            <el-table-column prop="create_time" label="时间" width="180" fixed="left" />
            <el-table-column prop="type" label="事件类型" width="120" :filters="[
                { text: '故障告警', value: 'ALARM' },
                { text: '运行状态', value: 'STATUS' },
                { text: '设备事件', value: 'EVENT' }
            ]" :filter-method="filterEventType" filter-placement="bottom">
                <template #default="{ row }">
                    <el-tag :type="getEventTypeTag(row.type)">
                        {{ getEventTypeLabel(row.type) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="name" label="事件名称" width="150" />
            <el-table-column prop="level" label="事件等级" width="100">
                <template #default="{ row }">                    
                        {{ getEventLevelLabel(row.level) }}                    
                </template>
            </el-table-column>

            <el-table-column prop="data" label="事件数据" min-width="200">
                <template #default="{ row }">
                    <!-- 修改后：将对象转换为 key=value 格式的字符串 -->
                    {{ formatObjectToString(row.data) }}
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[50, 100]"
                layout="total, sizes, prev, pager, next" :total="totalCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>
        <!-- 数据曲线 -->
        <el-dialog v-model="historyChart" title="数据曲线" width="50%" height="50%">
            <HistoryChart :data="historyChartData" :chart_series="chart_series" />
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Download, TrendCharts } from '@element-plus/icons-vue'
import { getDevEventHistoryApi } from '@/api/data'
import { useProductsStore } from '@/stores/modules/products'
import HistoryChart from '@/views/chart/historyChart.vue'

const props = defineProps({
    dev_id: {
        type: String,
        required: true
    }
})

const pdStore = useProductsStore()
const timeRange = ref([])
const historyData = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(50)
const totalCount = ref(0)
const eventLevel = ref('')
const eventType = ref('')
const defaultTime = [
    new Date(2025, 1, 1, 0, 0, 0),
    new Date(2025, 1, 1, 23, 59, 59)
]
// 查看数据曲线
const historyChart = ref(false)
const historyChartData = ref([])
const chart_series = ref([])
const showHistoryChart = () => {
    // 整理数据格式
    const { result, eventNamesArray } = countEventData()    
    historyChartData.value = result  // 假设这是用于图表显示的数据
    chart_series.value = eventNamesArray
    // 弹出对话框
    historyChart.value = true
}

const countEventData = () => {
    // 1. 获取所有不同的事件名称
    const eventNames = [...new Set(historyData.value.filter(item => item.type === 'ALARM').map(item => item.name))]

    // 2. 按日期分组
    const dateGroups = historyData.value.reduce((groups, item) => {
        // 提取日期部分
        const date = item.create_time.split(' ')[0]
        if (!groups[date]) {
            groups[date] = {}
            groups[date].create_time = date
            // 初始化所有事件计数为0
            eventNames.forEach(name => groups[date][name] = 0)
        }
        // 只增加名字在eventNames中的事件的计数
        if (eventNames.includes(item.name)) {
            groups[date][item.name]++
        }
        return groups
    }, {})

    // 3. 转换为目标格式
    const result = Object.entries(dateGroups).map(([date, counts]) => ({
        //create_time: date,
        ...counts
    }))

    //去除计数为0的
    result.forEach(item => {
        Object.keys(item).forEach(key => {
            if (item[key] === 0) {
                delete item[key]
            }
        })
    })
    // 4. 按日期排序
    result.sort((a, b) => a.create_time.localeCompare(b.create_time))

    //eventNames改为对象数组，{name:name,identifier:name}
    const eventNamesArray = eventNames.map(name => ({ name, identifier: name,type:'bar' }))
    
    return { result, eventNamesArray }
}

// 获取历史事件数据
const fetchHistoryData = async () => {
    if (!timeRange.value || !timeRange.value[0] || !timeRange.value[1]) {
        ElMessage.warning('请选择时间范围')
        return
    }

    loading.value = true
    try {
        const [startTime, endTime] = timeRange.value
        const params = {
            dev_id: props.dev_id,
            start_time: startTime,
            end_time: endTime,
            level: eventLevel.value,
            page: currentPage.value,
            page_size: pageSize.value
        }

        const { data, total } = await getDevEventHistoryApi(params)
        console.log('历史事件', data)
        historyData.value = data || []
        totalCount.value = total || data.length
    } catch (error) {
        ElMessage.error('获取历史事件失败')
        console.error('获取历史事件失败:', error)
    } finally {
        loading.value = false
    }
}

// 获取事件类型标签样式
const getEventTypeTag = (type) => {
    const map = {
        'ALARM': 'warning',
        'STATUS': 'danger',
        'EVENT': 'info'
    }
    return map[type] || 'info'
}
// 获取事件类型显示文本
const getEventTypeLabel = (type) => {
    const map = {
        'ALARM': '故障告警',
        'STATUS': '运行状态',
        'EVENT': '设备事件'
    }
    return map[type] || type
}
// 获取事件等级标签样式
const getEventLevelTag = (level) => {
    const map = {
        'DEBUG': 'info',
        'INFO': 'info',
        'WARN': 'warning',
        'ERROR': 'danger',
        'FATAL': 'danger'
    }
    return map[level] || 'info'
}
// 获取事件等级显示文本
const getEventLevelLabel = (level) => {
    const map = {
        'DEBUG': '调试',
        'INFO': '提示',
        'WARN': '告警',
        'ERROR': '错误',
        'FATAL': '严重'
    }
    return map[level] || level
}

// 添加格式化函数
const formatObjectToString = (obj) => {
    if (!obj) return '';
    return Object.entries(obj)
        .map(([key, value]) => `${key}=${value}`)
        .join(', ');
}

// 过滤事件类型
const filterEventType = (value, row) => {
    return row.type === value
}

// 导出数据
const exportData = async () => {
    if (!historyData.value.length) {
        ElMessage.warning('暂无数据可导出')
        return
    }

    try {
        // 准备导出数据
        const exportData = historyData.value.map(item => ({
            '时间': item.create_time,
            '事件类型': getEventTypeLabel(item.type),
            '事件标识符': item.identifier,
            '事件名称': item.name,
            '事件描述': item.description,
            '事件数据': JSON.stringify(item.data)
        }))

        // 创建工作簿
        const wb = XLSX.utils.book_new()
        const ws = XLSX.utils.json_to_sheet(exportData)
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

        // 导出文件
        const fileName = `设备事件历史_${formatTime(new Date())}.xlsx`
        XLSX.writeFile(wb, fileName)
    } catch (error) {
        ElMessage.error('导出失败')
        console.error('导出失败:', error)
    }
}

// 格式化时间
const formatTime = (time) => {
    return new Date(time).toLocaleString()
}

// 分页处理
const handleSizeChange = (val) => {
    pageSize.value = val
    fetchHistoryData()
}

const handleCurrentChange = (val) => {
    currentPage.value = val
    fetchHistoryData()
}

// 初始化
onMounted(async () => {
    // 设置默认时间范围为最近24小时
    const end = new Date()
    const start = new Date()
    start.setDate(start.getDate() - 1)

    const endTime = formatDate(end)
    const startTime = formatDate(start)
    timeRange.value = [startTime, endTime]

    // 获取历史数据
    await fetchHistoryData()
})

//转格式化时间,YYYY-MM-DD HH:mm:ss
const formatDate = (date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    const second = date.getSeconds().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}
</script>

<style scoped>
.history-container {
    padding-top: 10px;
    height: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;
}

.filter-group {
    display: flex;
    align-items: center;
}

.buttons {
    padding-left: 20px;
    display: flex;
    gap: 10px;
}

/* 固定表头 */
:deep(.el-table__header-wrapper) {
    position: sticky;
    top: 0;
    z-index: 1;
}

.pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
}

:deep(.el-table .cell pre) {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>