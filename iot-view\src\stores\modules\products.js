import { defineStore } from 'pinia'
import { getDevsApi,getProductsApi,getAssDevsApi,getProductPropsApi,getDevDetailApi,getDevPropsApi } from '@/api/products'

const defineIcon = 'sailstar.png'
export const useProductsStore = defineStore('products', {
    state: () => ({
        products:[],//产品列表
        devs: [],//设备列表
        devDetails: [],//设备详情
        nodes: {},//设备的属性
        events: {},//设备的事件        

    }),
    //Getter 完全等同于 store 的 state 的计算值.
    //Getter 只是幕后的计算属性，所以不可以向它们传递任何参数。不过，你可以从 getter 返回一个函数，该函数可以接受任意参数：
    //当带参数使用时，就失去了响应式；需要在调用的地方用computed
    getters: {
        getDev: (state) => {
            return (dev_id) => {
                return state.devs.find(item => item.dev_id == dev_id)
            }
        },
        //获取设备列表
        getDevs: (state) => {
            return async () => {
                let devs=state.devs
                if(devs.length==0){
                    devs=await state.requestDevs()    //获取所有设备的简略信息
                }
                return devs
            }
        },
        getDevInfo: (state) => {    //获取设备简略信息
            return async (dev_id) => {
                let dev=state.devs.find(item => item.dev_id == dev_id)
                if(!dev){
                    let devs=await state.requestDevs()    //获取所有产量的简略信息
                    dev=devs.find(item => item.dev_id == dev_id)                    
                }
                return dev
            }
        },
        getDevsInfo: (state) => {    //获取设备简略信息
            return async (dev_ids) => {
                //判断dev_ids是否都在state.devs中，如果不在，则获取所有产量的简略信息
                let devs=state.devs
                //判断dev_ids是否都在devs中，如果不在，则重新查询   
                let devsInfo=devs.filter(item => dev_ids.includes(item.dev_id))
                if(devsInfo.length!=dev_ids.length){
                    devsInfo=await state.requestDevs()    //获取所有产量的简略信息
                    devsInfo=devsInfo.filter(item => dev_ids.includes(item.dev_id))
                }
                return devsInfo
            }
        },
        getDevDetail: (state) => {
            return async (dev_id) => {
                let dev=state.devDetails.find(item => item.dev_id == dev_id)
                if(!dev){
                    dev=await state.requestDeviceDetail(dev_id)                    
                }
                return dev
            }
        },
        getProduct: (state) => {
            return async (pro_id) => {
                let pro=state.products.find(item => item.pro_id == pro_id)
                if(!pro){
                    pro=await state.requestProduct(pro_id)                    
                }
                return pro
            }
        },
        getProducts: (state) => {
            return async () => {
                let products=state.products
                if(products.length==0){
                    products=await state.requestProducts()
                }
                return products
            }
        },

        getDevProps: (state) => {
            return async (dev_id) => {
                if(!state.nodes[dev_id]){
                    await state.requestDevProps(dev_id) 
                }
                return state.nodes[dev_id]               
            } 
        }
    },
    actions: {
        //向后端请求产品列表
        async requestProducts() {
            //先检查本地是否存在            
            const data = await getProductsApi();
            this.products = data
            return data
        },
        
        //向后端请求单个产品
        async requestProduct(pro_id) {
            const data = await getProductsApi(pro_id);//返回列表
            //先删除仓库中已有的
            this.products=this.products.filter(item=>item.pro_id!=pro_id)
            //再添加新的
            this.products.push(data[0]);
            return data[0]
        },
        //向后端请求设备列表，简略信息
        async requestDevs() {
            const data = await getDevsApi();            
            this.devs = data
            return data
        },
        //向后端请求单个设备详情
        async requestDeviceDetail(dev_id) { 
            const data = await getDevDetailApi(dev_id);             
            //先删除仓库中已有的
            this.devDetails=this.devDetails.filter(item=>item.dev_id!=dev_id)
            //再添加新的
            this.devDetails.push(data);
            console.log('请求设备详情',data,this.devDetails)
            return data
        },
        //向后端请求产品的子设备列表,附带pro_id
        async requestAssDevs(pro_id) {
            const data = await getAssDevsApi(pro_id);             
            return data
        },
        //向后端请求产品属性列表
        async requestProductProps(pro_id) {
            console.log('请求属性',pro_id)
            const data = await getProductPropsApi(pro_id);
            this.nodes[pro_id] = data
            return data
        },
        //向后端请求设备属性列表
        async requestDevProps(dev_id) {
            const data = await getDevPropsApi(dev_id);
            this.nodes[dev_id] = data
            return data            
        },
        //向后端请求设备事件列表
        async requestDevEvents(dev_id) {
            console.log('请求事件',dev_id)
            const data = await getDeviceEventApi(dev_id);
            this.events[dev_id] = data.nodes            
            return data.nodes
        }
    },
})
