<template>
  <!-- 属性定义弹窗 -->
  <div class="property-form">
    <!-- 标题栏 -->
    <div class="header">
      <el-page-header :content="isEdit ? '编辑属性' : '新建属性'" />
    </div>
    
      <div class="tip">
        属性-设备数据；事件-设备事件，当值为1时触发事件；指令-设备可执行的命令。
      </div>
    
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" size="default" >
      <el-form-item label="属性名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入属性名称"></el-input>
      </el-form-item>

      <el-form-item label="属性标识符" prop="identifier">
        <el-input v-model="formData.identifier" placeholder="请输入唯一标识符"></el-input>
      </el-form-item>

      <el-form-item label="属性类型" prop="type">
        <el-radio-group v-model="formData.type">
          <el-radio label="PROP">属性</el-radio>
          <el-radio label="EVENT">事件</el-radio>
          <el-radio label="CMD">指令</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="数据类型" prop="data_type">
        <el-select v-model="formData.data_type" placeholder="选择数据类型">
          <el-option label="Number" value="Number"></el-option>
          <el-option label="Boolean" value="BOOL"></el-option>
          <el-option label="String" value="STRING"></el-option>
          <el-option label="Object" value="OBJECT"></el-option>
          <el-option label="Array" value="ARRAY"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="单位" prop="unit"></el-form-item>
      <!-- 保存方式，不保存，短期，长期 -->
      <el-form-item label="保存方式" prop="save_type">
        <el-select v-model="formData.save_type">
          <el-option label="不保存" value="NONE"></el-option>
          <el-option label="短期" value="SHORT"></el-option>
          <el-option label="长期" value="LONG"></el-option>
        </el-select>
      </el-form-item>      
    </el-form> 
    <div class="footer">
      <el-button type="primary" @click="submitForm">{{ isEdit ? '更新' : '新建' }}</el-button>
    </div>   
  </div>
</template>

<script setup>
import { ref, watch,defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { updateProductPropApi } from '@/api/products'

const props = defineProps({
  pro_id: {
    type: String,
    required: true
  },
  propData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['close'])

const formRef = ref(null)
const isEdit = ref(false)

// 表单数据
const formData = ref({
  name: '',
  identifier: '',
  type: 'PROP',
  data_type: '',
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入属性名称', trigger: 'blur' }],
  identifier: [{ required: true, message: '请输入属性标识符', trigger: 'blur' }],
  data_type: [{ required: true, message: '请选择数据类型', trigger: 'change' }]
}

watch(() => props.propData, (newVal) => {
  // 重置表单
  formData.value = {
    name: '',
    identifier: '',
    type: 'PROP',
    pro_id: props.pro_id,
    data_type: ''
  }
  console.log('propData', newVal)
  if (newVal) {
    isEdit.value = true
    formData.value = { ...newVal }
  }
}, { immediate: true })


// 提交表单
const submitForm = async (continueAdd = true) => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    await updateProductPropApi(formData.value)
    ElMessage.success('更新成功')
    // 通知父组件刷新列表        
    formRef.value.resetFields() 
    emit('close')   
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '添加失败')
    console.error('操作失败:', error)
  }
}

</script>
<style scoped>
.property-form {
  padding: 20px;
  height: 100%;
  overflow-y: auto;   
}
.header {
    margin-bottom: 20px;
}
.tip{
    color: #999;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 20px;
}
.el-form-item {
    .el-input {
        width: 300px;
        height: 40px;
    }

    .el-select {
        width: 300px;
        height: 40px;
    }
}
.rule-form {
    max-width: 600px;
}
</style>