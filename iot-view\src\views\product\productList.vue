//所有设备的列表，
<template>
    <div class="container">
        <div class="page-header">
            <h2 class="page-title">产品</h2>
            <el-button type="primary" @click="editProduct" >创建新产品</el-button>
        </div>
        <!-- 编辑产品 -->
        <el-dialog v-model="editProductDialog" title="新建产品" width="60%">
            <product-edit  @success="handleEditSuccess" />
        </el-dialog>
        
        <el-row>
            <el-col :span="4" v-for="device in products" :key="device.pro_id">                
                    <el-card class="device-card" shadow="hover" @click="goDetail(device.pro_id)">
                        <div class="device-header">
                            <img class="device-icon" src="/images/sailstar.png" alt="设备图标" />
                            <div class="device-info">
                                <div class="device-name">{{ device.name }}</div>
                                <div class="device-type">{{ device.type }}</div>
                            </div>
                        </div>
                    </el-card>                
            </el-col>
        </el-row>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElSelect, ElOption, ElInput, ElButton, ElRow, ElCol, ElCard } from 'element-plus';
import { useProductsStore } from '@/stores/modules/products';
import ProductEdit from './productEdit.vue';
import { useRouter } from 'vue-router';


const products = ref([])
const pdStore = useProductsStore();
onMounted(async () => {
    products.value = await pdStore.requestProducts();
});

const editProductDialog = ref(false);
const editProduct = () => {
    editProductDialog.value = true;
}
//TODO: 新建产品
const handleEditSuccess = async () => {
    editProductDialog.value = false;
    //重新获取产品列表
    products.value = await pdStore.requestProducts();
}   
//TODO: 跳转产品详情
const router = useRouter();
const goDetail = (pro_id) => {
    
    router.push({
        path: '/product/' + pro_id        
    });
}
</script>

<style scoped>
.container {
    padding: 20px;
}
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}
.device-card {
    margin-bottom: 20px;
    margin: 5px;
    height: 120px;
}

.device-header {
    display: flex;
    flex-direction: column;
    align-items: center;    
}

.device-icon {
    width: 40px;
    height: 40px;    
}

.device-info {
    display: flex;
    flex-direction: column;
}

.device-name {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.device-type {
    font-size: 12px;
    color: var(--el-color-secondary);
}

.device-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.device-status {
    font-size: 14px;
    color: #333;
}

.page-title {
    margin: 20px 15px;
}
</style>