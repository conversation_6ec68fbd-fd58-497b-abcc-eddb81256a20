import { localGet, pathMap } from '@/utils'
import { useAccount } from './stores/modules/account'
import router from '@/router'

const whiteList = ['/login', '/auth-redirect'] // 不需要权限的地址
router.beforeEach(async (to, from, next) => {
    // 显示进度条 
  
  // 设备页面标题
  document.title = to.meta.title

  // 获取token
  const hasToken = localGet('sessionid')
  console.log(hasToken, 'sessionId')

  if (hasToken) {
    if (to.path === '/login') {
      // 如果访问登录页面，重定向到首页
      next()
    } else {
      const { userInfo, getUserInfo, generateRoutes } = useAccount()
      // 判断有无获取过用户权限，如果获取过直接跳转
      const hasRoles = userInfo && userInfo.roles && userInfo.roles.length > 0      

      if (hasRoles) {
        next()
      } else {
        //用户未获取权限，一般是初次访问，则需要去获取用户信息，权限
        try {
          const { roles } = await getUserInfo()
          console.log('getRoles', roles)
          const accessRoutes = await generateRoutes(roles)
          
          accessRoutes.forEach(r => router.addRoute(r))

          next({ ...to, replace: true })
        } catch (error) {
          console.log('获取信息出错', to.path)
          // remove token and go to login page to re-login  
          next(`/login?redirect=${to.path}`)
        }
      }
    }
  } else {
    //用户未登录
    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.      
      next(`/login?redirect=${to.path}`)
    }
  }
})

router.afterEach(() => {

  // finish progress bar  
})
