<template>
    <div v-if="device" class="container">
        <!-- 返回按钮和标题 -->
        <el-page-header @back="goBack">
            <template #content>
                <span class="text-large font-600 mr-3" style="margin-right: 10px;">{{ device.name }} </span>
                <el-tag :type="getStatusTag">{{ devStatus?.name,devStatus?.create_time }}</el-tag>
                <el-button :icon="Edit" type="primary" @click="editDevice" text />
            </template>
        </el-page-header>
        <el-divider style="margin: 20px 0px 10px 0px;" />

        <!-- 设备基本信息 -->
        <el-descriptions :column="6" class="compact-descriptions">
            <el-descriptions-item label="ID">{{ device.dev_id }}</el-descriptions-item>
            <el-descriptions-item label="所属产品"><span @click="goToProductDetail">{{ device.pro_name
                    }}</span></el-descriptions-item>
            <el-descriptions-item label="生产类型">{{ getDevProductionType }}</el-descriptions-item>
            <el-descriptions-item label="接入类型">{{ getDevConnectType }}</el-descriptions-item>
            <el-descriptions-item label="最后在线时间">{{ lastOnlineTime }}</el-descriptions-item>
        </el-descriptions>

        <!-- Tab 按钮 -->
        <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="device-tabs">
            <el-tab-pane label="连接" name="connection" />
            <el-tab-pane v-if="device.connect_type === 'GATEWAY'" label="网关接口" name="gwInterface" />
            <el-tab-pane v-if="device.connect_type === 'SUBDEV'" label="设备接口" name="devInterface" />
            <el-tab-pane label="属性" name="properties" />
            <el-tab-pane label="历史数据" name="history" />
            <el-tab-pane label="事件记录" name="eventHistory" />
            <el-tab-pane label="规则记录" name="ruleHistory" />
            <el-tab-pane label="任务" name="tasks" />
            <el-tab-pane label="调试" name="debug" />
            <el-tab-pane label="设置" name="settings" />

        </el-tabs>

        <!-- 动态组件 -->
        <component :is="currentComponent" :dev_id="device.dev_id" :pro_id="device.pro_id" />

        <!-- 设备未连接提示 -->
        <el-alert v-if="activeTab === 'overview'" title="设备尚未连接或上报数据到ThingsCloud" type="info" show-icon>
            请参考教程：
            <el-link type="primary" href="#">MQTT接入</el-link>
            <el-link type="primary" href="#">DTU/RTU接入</el-link>
            <el-link type="primary" href="#">4G/NB-IoT模组接入</el-link>
            <el-link type="primary" href="#">WIFI模组接入</el-link>
            <el-link type="primary" href="#">更多设备接入指南</el-link>
        </el-alert>
        <!-- 编辑设备 -->
        <el-dialog v-model="editDeviceDialog" title="编辑设备" width="50%" destroy-on-close>
            <device-edit :dev_id="dev_id" @success="handleEditSuccess" />
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { Edit } from '@element-plus/icons-vue';
import { useRoute, useRouter } from 'vue-router';
import { getDevsLastOnlineTimeApi,getDevsStatusApi,getDevsStatusCurrentApi } from '@/api/data';
import { getDevDetailApi } from '@/api/products'
import { ElMessage,ElTag } from 'element-plus';
// 导入各个 Tab 对应的组件
//import Overview from './components/Overview.vue';
import Connection from './components/Connection.vue';
import Properties from '../prop/Properties.vue';
import GwInterface from './components/GwInterface.vue';
import DevInterface from './components/DevInterface.vue';
import PropertyHistory from '../prop/PropertyHistory.vue';
import EventHistory from '../alarm/EventHistory.vue';
import RuleHistory from '../rule/ruleHistory.vue';
import deviceEdit from './deviceEdit.vue';


const route = useRoute();
const router = useRouter();


const dev_id = ref(route.params.dev_id);
const device = ref({})
watch(() => route.params.dev_id, async (newId) => {
    if (!newId || newId === ':dev_id') {        
        return;
    }
    dev_id.value = newId;
    device.value = null; // 清空当前设备数据
    let res = await getDevDetailApi(dev_id.value);
    device.value = res;
    console.log('device.value', device.value);
}, { immediate: true });


const devStatus = ref({})
const lastOnlineTime = ref(null)


const editDeviceDialog = ref(false)
//编辑设备
const editDevice = () => {
    editDeviceDialog.value = true
}
const handleEditSuccess = async () => {
    editDeviceDialog.value = false
    device.value = await getDevDetailApi(dev_id.value)
}

onMounted(async () => {    
    //确保获取到设备详情后，自动切换到连接Tab
    activeTab.value = 'connection';

    //获取设备最新状态
    let res=await getDevsStatusCurrentApi([dev_id.value])
    if (res.length > 0){
        devStatus.value=res[0]
    }
    
    //获取最后在线时间    
    let res2=await getDevsLastOnlineTimeApi({ dev_ids: [dev_id.value] })
    if (res2.length > 0)
        lastOnlineTime.value = res2[0].create_time
    else
        lastOnlineTime.value = '--'
    
});

const getDevConnectType = computed(() => {
    const connect_name = {
        GATEWAY: '网关',
        SUBDEV: '网关子设备',
        DIRECT: '直连设备'
    };
    return connect_name[device.value.connect_type] || '未知';
});
const getDevProductionType=computed(()=>{
    const production_type={
        "PROG":'程序产出',
        "TOTAL":'连续产出',
        'NONE':'无产出'
    }
    return production_type[device.value.production]
})

const getStatusTag = computed(() => {
    const tagMap = {
        'RUN': 'success',
        'STANDBY': 'warning',
        'OFFLINE': 'danger',
        'FAULT': 'danger',
        'ALARM': 'danger'
    }
    console.log('设备状态',devStatus.value)
    //判断是否空值
    
    if (devStatus.value.status!==undefined){
        return tagMap[devStatus.value.status]
    }
    return 'danger'
})

const activeTab = ref(''); // 默认激活的 Tab

//根据 activeTab 动态加载组件
const currentComponent = computed(() => {
    switch (activeTab.value) {
        // case 'overview':
        //     return Overview;
        case 'connection':
            return Connection;
        case 'properties':
            return Properties;
        case 'gwInterface':
            return GwInterface;
        case 'devInterface':
            return DevInterface;
        case 'history':
            return PropertyHistory;
        case 'eventHistory':
            return EventHistory;
        case 'ruleHistory':
            return RuleHistory;
    }
});


// 返回上一页
const goBack = () => {
    //返回上一页面
    router.back();
};

// Tab 点击事件
const handleTabClick = (tab) => {
    console.log('切换到 Tab:', tab.props.name);
    activeTab.value = tab.props.name;
};

// 跳转到产品详情页
function goToProductDetail(event) {
    console.log('goToProductDetail', device.value.pro_id)
    router.push(`/product/${device.value.pro_id}`);
};



</script>

<style scoped>
.container {
    padding: 20px;
}

.device-detail {
    margin: 20px auto;
    padding: 20px;
    max-width: 1200px;

}

.device-tabs {
    margin-top: 20px;
}

.compact-descriptions {
    width: 80%;
    /* 调整宽度为父容器的50%，你可以根据需要调整这个值 */
    margin: 0 0;
    /* 水平居中 */
    text-align: left;
}

:deep .el-descriptions__body {
    background: #ffffff00 !important;
}
</style>