<template>
  <div class="app-wrapper">
    <div v-if="!$route.meta.isFull" class="sidebar-container">
      <NavMenu />
    </div>
    <div class="main-container" :class="{ 'has-sidebar': !$route.meta.isFull }">
      <RouterView />
    </div>
  </div>
</template>

<script setup>
import { RouterView } from 'vue-router'
import NavMenu from '../components/NavMenu.vue'
import { useRouter } from 'vue-router'
import { useProductsStore } from '../stores/modules/products'
import { ref, computed, onMounted, watch } from 'vue';

const pdStore = useProductsStore()

onMounted(() => {

})
</script>

<style scoped>
.app-wrapper {
  height: 100vh;
  width: 100%;
  display: flex;
  overflow: hidden;
}

.sidebar-container {
  height: 100%;
  transition: width 0.3s;
  background-color: #ffffff;
  z-index: 1001;
  flex-shrink: 0;
  overflow: auto;
  border-right: 1px solid #e4e7ed;
}

.main-container {
  flex: 1;
  height: 100%;
  overflow: auto;  
  background-color: #f0f2f5;
  transition: margin-left 0.3s;
  box-sizing: border-box;
}

.main-container.has-sidebar {
  margin-left: 0;
  width: calc(100% - 200px);
}

/* 当侧边栏折叠时的样式 */
.nav-menu.is-collapse+.main-container.has-sidebar {
  width: calc(100% - 64px);
}
</style>
