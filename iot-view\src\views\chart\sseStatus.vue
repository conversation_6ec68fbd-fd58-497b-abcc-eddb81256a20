<template>
    <!-- WebSocket连接状态 -->
    <div>
        <el-tag :type="sseStatusType" class="status-tag" size="small">
            SSE实时数据{{ sseClient.isConnected.value ? '已连接' : '未连接' }}
        </el-tag>

    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import sseClient from '@/utils/sse'

// 在组件中监听连接状态
const sseStatusType = computed(() => sseClient.isConnected.value ? 'success' : 'danger')

</script>

<style scoped>
    .socket-status {
        margin-bottom: 20px;
    }

    .status-tag {
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    :deep(.el-icon) {
        margin-right: 2px;
    }
</style>
