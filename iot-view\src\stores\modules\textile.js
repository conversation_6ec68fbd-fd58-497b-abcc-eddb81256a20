import { defineStore } from 'pinia'
import { getHotelsApi,getTextilesApi } from '@/api/textile'

const defineIcon = 'sailstar.png'
export const useTextileStore = defineStore('textile', {
    state: () => ({
        hotels:[],//酒店列表
        textiles: [],//纺织品列表
    }),
    //Getter 完全等同于 store 的 state 的计算值.
    //Getter 只是幕后的计算属性，所以不可以向它们传递任何参数。不过，你可以从 getter 返回一个函数，该函数可以接受任意参数：
    //当带参数使用时，就失去了响应式；需要在调用的地方用computed
    getters: {
        getHotelInfo: (state) => {    //获取酒店简略信息
            return async (hotel_id) => {
                let hotel=state.hotels.find(item => item.hotel_id == hotel_id)
                if(!hotel){
                    let hotels=await state.requestHotels()    //获取所有产量的简略信息
                    hotel=hotels.find(item => item.hotel_id == hotel_id)
                }
                return hotel
            }
        },
        getTextileInfo: (state) => {
            return async (tex_id) => {
                let tex=state.textiles.find(item => item.tex_id == tex_id)                
                if(!tex){
                    let texs=await state.requestTextiles()  
                    tex=texs.find(item => item.tex_id == tex_id)                  
                }
                return tex
            }
        }
    },
    actions: {
        //向后端请求所有酒店列表
        async requestHotels() {
            const data = await getHotelsApi(); 
            this.hotels = data
            return data         

        },
        //向后端请求单个酒店详情
        async requestHotel(hotel_id) {
            let d={}
            if(hotel_id) d.hotel_ids=[hotel_id]
            
            const data = await getHotelsApi(d); 
            if (data.length===0) {
                return null
            }
            let hotel = data[0]
            //如果存在，则更新，否则新增
            const index = this.hotels.findIndex(hotel => hotel.hotel_id === hotel.hotel_id)
            if(index !== -1) {
                this.hotels[index] = hotel
            } else {
                this.hotels.push(hotel)
            }
            return hotel
        },

        //向后端请求设备列表，简略信息
        async requestTextiles() {
            const data = await getTextilesApi();            
            this.textiles = data
            return data
        }
        
    },
})
