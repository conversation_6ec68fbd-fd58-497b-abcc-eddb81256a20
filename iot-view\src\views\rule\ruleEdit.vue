<template>
    <div class="rule-add">
        <!-- 标题栏 -->
        <div class="header">
            <el-page-header @back="goBack" title="返回" :content="pageTitle" />
        </div>

        <!-- 表单内容 -->
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="rule-form">
            <!-- 规则名称 -->
            <el-form-item label="规则名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入规则名称" />
            </el-form-item>

            <!-- 规则类型 -->
            <el-form-item label="规则类型" prop="type">
                <el-select v-model="formData.type" placeholder="请选择规则类型">
                    <el-option label="属性上报预处理" value="PRE" />
                    <el-option label="属性上报操作" value="PROP" />
                    <el-option label="事件上报操作" value="EVENT" />
                </el-select>
            </el-form-item>

            <!-- 规则对象类型 -->
            <el-form-item label="规则对象类型" prop="target_type">
                <el-radio-group v-model="formData.target_type" @change="handletypeChange">
                    <el-radio label="PRODUCT">产品</el-radio>
                    <el-radio label="DEVICE">设备</el-radio>
                </el-radio-group>
            </el-form-item>

            <!-- 产品/设备选择 -->
            <el-form-item label="选择对象" prop="target_id">
                <!-- 当type为device时，多选；否则单选 -->
                <el-select v-model="formData.target_id" :multiple="true" :multiple-limit="objectLimit"
                    placeholder="请选择">
                    <el-option v-for="item in objectOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>

            <!-- 属性条件 -->
            <el-form-item label="属性条件" prop="properties">
                <div class="condition-settings">
                    <div v-for="(condition, index) in conditions" :key="index" class="condition-row">
                        <!-- Logical Operator -->
                        <el-select v-model="condition.logical_operator" placeholder="AND" style="width: 100px">
                            <el-option label="AND" value="AND" />
                            <el-option label="OR" value="OR" />
                        </el-select>

                        <!-- Attribute -->
                        <el-select v-model="condition.attribute" placeholder="请选择属性" style="width: 150px">
                            <el-option v-for="attr in propertyOptions" :key="attr.identifier"
                                :label="attr.identifier + '-' + attr.name" :value="attr.identifier" />
                        </el-select>

                        <!-- Operator -->
                        <el-select v-model="condition.operator" placeholder="请选择操作符" style="width: 120px">
                            <el-option label="大于" value=">" />
                            <el-option label="大于等于" value=">=" />
                            <el-option label="小于" value="<" />
                            <el-option label="小于等于" value="<=" />
                            <el-option label="等于" value="=" />
                            <el-option label="不等于" value="!=" />
                            <el-option label="范围" value="range" />
                            <el-option label="非范围" value="not_range" />
                        </el-select>

                        <!-- Threshold Min -->
                        <el-input v-model="condition.threshold_min" placeholder="最小值" style="width: 100px"
                            type="number" />

                        <!-- Threshold Max (only visible for range and not_range operators) -->
                        <el-input v-if="condition.operator === 'range' || condition.operator === 'not_range'"
                            v-model="condition.threshold_max" placeholder="最大值" style="width: 100px" type="number" />

                        <!-- Delete Button -->
                        <el-button @click="removeCondition(index)" type="danger" :icon="Delete" circle />
                    </div>

                    <!-- Add Condition Button -->
                    <el-button @click="addCondition" type="primary">添加条件</el-button>

                    <!-- Show JSON Output -->

                </div>
            </el-form-item>

            <!-- 预处理函数 -->
            <el-form-item label="预处理函数">
                <el-button @click="showCodeEditor">编辑预处理函数</el-button>
            </el-form-item>

            <!-- 规则描述 -->
            <el-form-item label="规则描述" prop="description">
                <el-input v-model="formData.description" placeholder="请输入规则描述" />
            </el-form-item>

            <!-- 提交按钮 -->
            <el-form-item>
                <el-button type="primary" @click="submitForm">{{ submitButtonText }}</el-button>
            </el-form-item>
        </el-form>

        <!-- 代码编辑器弹窗 -->
        <el-dialog v-model="codeDialogVisible" title="编辑预处理函数" width="60%">
            <CodeEditor v-if="codeDialogVisible" v-model="tempCode" @update:modelValue="handleUpdateCode"
                class="code-editor" />
            <template #footer>
                <el-button @click="cancelCode">取消</el-button>
                <el-button type="primary" @click="confirmCode">确定</el-button>
            </template>
        </el-dialog>
    </div>
    
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useProductsStore } from '@/stores/modules/products'
import { ElMessage } from 'element-plus'
import CodeEditor from './components/codeEdit.vue'
import { Delete } from '@element-plus/icons-vue'
import { getRuleDetailApi, updateRuleApi } from '@/api/products'
const router = useRouter()
const route = useRoute()
const productsStore = useProductsStore()

// 表单数据
const formData = reactive({
    name: '',
    type: '',
    target_type: 'PRODUCT',
    target_id: [],
    conditions: [],
    code: '',
    description: ''
})

// 表单验证规则
const rules = {
    name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择规则类型', trigger: 'change' }],
    target_id: [{ required: true, message: '请选择对象', trigger: 'change' }],
    description: [{ required: true, message: '请输入规则描述', trigger: 'blur' }]
}

// 代码编辑器显示控制
const codeDialogVisible = ref(false)
const formRef = ref(null)
const objectLimit = ref(0)  //选择产品或设备的最大数量

// 计算属性：对象选项列表
const objectOptions = computed(() => {
    if (formData.target_type === 'PRODUCT') {
        objectLimit.value = 1

        return productsStore.products.map(item => ({
            id: item.pro_id,
            name: item.name
        }))

    } else {
        objectLimit.value = 0
        return productsStore.devs.map(item => ({
            id: item.dev_id,
            name: item.name
        }))
    }
})

// 属性选项列表
const propertyOptions = ref([])

//当选择的产品或设备改变时，重新获取属性
watch(() => formData.target_id, async (new_ids) => {
    console.log('formData.target_id改变', new_ids)
    if (new_ids.length === 0) return

    //判断是否数组格式
    if (!Array.isArray(new_ids)) {
        formData.target_id = [new_ids]
    }

    let pro_id = formData.target_id[0]
    if (formData.target_type === 'DEVICE') {
        const dev = productsStore.devs.find(item => item.dev_id === formData.target_id[0])
        pro_id = dev.pro_id
    }
    propertyOptions.value = await productsStore.requestProductProps(pro_id)
})

// 判断是否为编辑模式 (修改路由参数判断)
const isEdit = computed(() => {
    const path = route.path
    return path.startsWith('/rule/')
})
// 获取规则ID (修改获取ID的方式)
const getRuleId = computed(() => {
    if (!isEdit.value) return null
    return route.path.split('/').pop()
})

const rule_id = ref(route.params.rule_id);
console.log('rule_id', rule_id.value)


// 修改提交按钮文本
const submitButtonText = computed(() => isEdit.value ? '更新规则' : '创建规则')

// 页面标题计算属性
const pageTitle = computed(() => isEdit.value ? '编辑规则' : '创建规则')

//################################################################################
// 临时存储代码的变量
const tempCode = ref('')

// 显示代码编辑器
const showCodeEditor = () => {
    // 如果临时代码为空，则使用表单中的代码
    if (!tempCode.value) {
        tempCode.value = formData.code || ''
    }
    codeDialogVisible.value = true
}

// 取消编辑代码
const cancelCode = () => {
    // 只关闭对话框，不清空临时代码
    codeDialogVisible.value = false
    // 清空临时代码
    //tempCode.value = ''
}

// 确认代码
const confirmCode = () => {
    // 将临时代码保存到表单数据中
    formData.code = tempCode.value
    codeDialogVisible.value = false
}
//################################################################################
// 获取代码编辑器中的代码
const handleUpdateCode = (code) => {
    // 更新临时代码
    tempCode.value = code
}

// 获取规则详情
const fetchRuleDetail = async (id) => {
    try {
        const ruleDetail = await getRuleDetailApi(id)
        console.log('ruleDetail', ruleDetail)
        // 填充表单数据
        Object.assign(formData, ruleDetail[0])
        // 解析conditions
        formData.conditions = JSON.parse(ruleDetail[0].conditions || '[]')
        //填充conditions
        conditions.value = formData.conditions
        // 如果有预处理代码，也要保存
        if (ruleDetail[0].code) {
            formData.code = ruleDetail[0].code
        }
    } catch (error) {
        ElMessage.error('获取规则详情失败')
    }
}

// 初始化加载
onMounted(async () => {
    await Promise.all([
        productsStore.requestProducts(),
        productsStore.requestDevs()
    ])

    // 如果是编辑模式，获取规则详情
    if (isEdit.value) {
        await fetchRuleDetail(getRuleId.value)
    }
})

// 对象类型改变处理
const handletypeChange = () => {
    formData.target_id = []
}

// 对象选择改变处理
const handleObjectSelect = () => {
    //确保单选模式返回数组    
    if (formData.type === 'device') {
        formData.target_id = [formData.target_id]
    }
}

const conditions = ref([
    {
        logical_operator: "AND",
        attribute: "",
        operator: "",
        threshold_min: null,
        threshold_max: null
    }
]);
// 添加一个条件
const addCondition = () => {
    conditions.value.push({
        logical_operator: "AND",
        attribute: "",
        operator: "",
        threshold_min: null,
        threshold_max: null
    });
};

// 删除某个条件
const removeCondition = (index) => {
    conditions.value.splice(index, 1);
};

// 转换为 JSON 格式
const jsonConditions = computed(() => {
    formData.conditions = JSON.stringify(conditions.value, null, 2)    
    return formData.conditions;
});

// 返回上一页
const goBack = () => {
    router.back()
}

// 提交表单
const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
        if (valid) {
            try {
                // const newForm = new FormData()
                // newForm.append('name', formData.name)
                // newForm.append('type', formData.type)
                // newForm.append('target_type', formData.target_type)
                // newForm.append('target_id', formData.target_id)
                // newForm.append('conditions', formData.conditions)
                // newForm.append('code', formData.code)
                // newForm.append('description', formData.description)
                formData.conditions = JSON.stringify(conditions.value, null, 2)
                if (isEdit.value) {
                    formData.id = getRuleId.value  // 使用新的ID获取方式
                    // TODO: 调用更新规则API
                    await updateRuleApi(formData)
                    ElMessage.success('规则更新成功')
                } else {
                    // TODO: 调用创建规则API
                    await updateRuleApi(formData)
                    ElMessage.success('规则创建成功')
                }
                router.back()
            } catch (error) {
                ElMessage.error(isEdit.value ? '规则更新失败' : '规则创建失败')
            }
        }
    })
}
</script>

<style scoped>
.rule-add {
    padding: 20px;
}

.header {
    margin-bottom: 20px;
}

.rule-form {
    max-width: 800px;
}

.code-textarea {
    :deep(.el-textarea__inner) {
        font-family: Consolas, Monaco, 'Courier New', monospace;
        font-size: 14px;
        line-height: 1.6;
        background-color: #1e1e1e;
        color: #d4d4d4;
        padding: 12px;
    }
}

.el-dialog__body {
    padding: 20px;
    background-color: #1e1e1e;
}

.condition-settings {
    margin: 20px;
}

.condition-row {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}
.el-form-item {
    .el-input {
        width: 300px;
        height: 38px;
    }
    .el-select {
        width: 300px;
        height: 38px;
    }
}
</style>
