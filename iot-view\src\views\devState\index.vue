<!--设备总图状态，大屏外层，根据条件引入不同的布局方式 -->
<template>
  <component :is="bigscreenLayout"></component>

</template>


<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useAccount } from '../../stores/modules/account'
//import layoutSS from './layoutSS.vue'
import layout from './layout.vue'

const accStore = useAccount()
const bigscreenLayout = ref(layout)

onMounted(() => {  
  if (accStore.userInfo.layout && accStore.userInfo.layout.bigscreen) {
    // if (accStore.userInfo.layout.bigscreen == '大屏布局A') {
    //   bigscreenLayout.value = layoutSS
    // }

  }
})
</script>


<style scoped>
.bg {
  height: 100%;
  width: 100%;
  background-color: #020308;
  padding: 16px 16px 0 16px;
  background-image: url('../../../public/images/bg.png');
  background-size: cover;
  background-position: center center;
}

.devicon {
  height: 100%;
}

.devicon img {
  width: 100%;
  object-fit: contain;
  /* 图片缩放方式：fill拉伸/缩小填充；contain保持宽高缩放填充；cover保持宽高比填充，超出部分裁剪 */
}

.devicon .danger {
  /* 图片遮罩 */
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(35, 19, 208, 0.2);
  z-index: 3000;
}

.devicon .info {
  display: none;
}

.devicon .success {
  display: none;
}
</style>