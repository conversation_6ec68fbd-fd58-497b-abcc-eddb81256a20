import layout from '../layout/layout.vue'

export const asyncRoutes = [
  {
    path: '/permission',
    name: 'Permission',
    // route level code-splitting
    // this generates a separate chunk (About.[hash].js) for this route
    // which is lazy-loaded when the route is visited.
    component: layout,
    redirect: '/about',
    meta: {
      title: 'Permission',
      roles: ['admin', 'user'] // you can set roles in root nav
    },
    children: [
      {
        path: '/about',
        name: 'about',
        // route level code-splitting
        // this generates a separate chunk (About.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: () => import('../views/AboutView.vue'),
        meta: {
          title: '有权限页面',
          roles: ['admin'] // you can set roles in root nav
        },
      }
    ]
  },




];