<!-- 设备总产量曲线图，可输入多组，如水洗组，烘干组，后整理组 -->
<template>
    <div class="dev-status-pie">
        <v-chart class="chart" :option="chartOption" autoresize />
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import VChart from 'vue-echarts'
import * as echarts from "echarts"

import { getDevsProductionApi } from '@/api/aggregate'
import { useProductsStore } from '@/stores/modules/products'
import { ElMessage } from 'element-plus'
const pdStore = useProductsStore()
const props = defineProps({
    dev_ids_1: {
        type: Array,
        default: () => []
    },
    dev_ids_2: {
        type: Array,
        default: () => []
    },
    dev_ids_3: {
        type: Array,
        default: () => []
    },

    //时间范围，[startTime,endTime]
    time_range: {
        type: Array,
        default: () => []
    },
    //时间单位
    unit: {
        type: String,
        default: 'hour'
    },
    //图表名称
    series_name: {
        type: Array,
        default: () => ["第一组", "第二组", "第三组"]
    }
})

const chartData = ref([])
const chartProps = ref([])
const loading = ref(false)

// 图表配置
const chartOption = computed(() => ({
    tooltip: {// 提示框
        show: true,
        trigger: 'axis',

    },
    toolbox: {
        feature: {
            magicType: {  // 动态类型切换
                type: ['line', 'bar', 'stack']
            }
        },
        right: '5%'
    },
    legend: {//曲线的图示
        data: chartProps.value?.map(prop => prop.name),
        top: '1%',
        textStyle:{
            color: '#FAFAFA'
        }
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '5%',  // 增加底部空间，为数据区域滚动条留出位置
        containLabel: true
    },
    xAxis: {// X轴
        type: 'time',
    },
    yAxis: {// Y轴
        type: 'value',
    },
    dataset: {// 数据集
        dimensions: ['create_time', ...chartProps.value?.map(prop => prop.identifier)],
        source: chartData.value || []
    },
    series: chartProps.value?.map(prop => ({// 数据系列,如果是total改为line
        type: 'line',
        name: prop.name,
        smooth: true,
        areaStyle: {},        
        encode: {
            x: 'create_time',
            y: prop.identifier
        }
    }))
}))


const fetchProduction = async () => {
    const [startTime, endTime] = props.time_range
    if (!startTime || !endTime) {
        ElMessage.error('请选择时间范围')
        return
    }
    loading.value = true
    try {
        //三个数组合并
        let dev_ids = [...props.dev_ids_1, ...props.dev_ids_2, ...props.dev_ids_3]
        const params = {
            dev_ids: dev_ids,
            start_time: startTime,
            end_time: endTime,
            time_type: props.unit
        }
        let res = []
        res = await getDevsProductionApi(params)
        console.log('每小时产量', res)

        chartData.value = groupTotalByCreateTime(res) || [];
    } catch (error) {
        ElMessage.error('获取每小时产量失败')
        console.error('获取每小时产量失败:', error)
    } finally {
        loading.value = false
    }
}


const groupTotalByCreateTime = (data) => {
    const resultMap = {};
    // 遍历原始数据,data是一个对象数组,转为一个按时间合并的对象数组
    data.map(item => {
        // 把create_time转换为YYYY-MM-DD
        const { create_time, dev_id, production } = item;
        // 如果当前时间点不存在，初始化一个对象
        if (!resultMap[create_time]) {
            resultMap[create_time] = { create_time };
        }
        // 根据dev_id属于哪个组，就把产量加到哪个组中
        if (props.dev_ids_1.includes(dev_id)) {
            resultMap[create_time]['group1'] = (resultMap[create_time]['group1'] || 0) + production
        } else if (props.dev_ids_2.includes(dev_id)) {
            resultMap[create_time]['group2'] = (resultMap[create_time]['group2'] || 0) + production
        } else if (props.dev_ids_3.includes(dev_id)) {
            resultMap[create_time]['group3'] = (resultMap[create_time]['group3'] || 0) + production
        }

    })

    // 将结果转换为数组    
    console.log('resultMap', Object.values(resultMap))
    return Object.values(resultMap)
}

//转格式化时间,YYYY-MM-DD HH:mm:ss
const formatDate = (date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    const second = date.getSeconds().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}
const getChartProps = () => {
    return [
        { name: props.series_name[0], identifier: 'group1' },
        { name: props.series_name[1], identifier: 'group2' },
        { name: props.series_name[2], identifier: 'group3' }
    ]
}

watch(() => props.time_range, async (newVal) => {
    await fetchProduction()
}, { deep: true })

onMounted(async () => {
    await fetchProduction()
    chartProps.value = getChartProps()
})
</script>

<style scoped>
.dev-status-pie {
    width: 100%;
    height: 100%;
    min-height: 200px;
}

.chart {
    width: 100%;
    height: 100%;
    min-height: 200px;
}
</style>