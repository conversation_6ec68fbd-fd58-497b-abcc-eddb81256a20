import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

import './assets/main.css'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
//import "@/styles/common.scss";

import { localGet, pathMap } from '@/utils'

//启用mock模拟数据
//import './mock/index'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
    size: localGet('size') || 'large', // set element-ui default size
    //locale: enLang // 如果使用中文，无需设置，请删除
})

import './permission' // permission control

//app.config.globalProperties.$echarts = echarts;//全局使用
app.mount('#app')


