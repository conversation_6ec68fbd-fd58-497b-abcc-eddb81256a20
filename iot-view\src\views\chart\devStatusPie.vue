<template>
    <!-- 设备状态饼图 -->
    <div class="dev-status-pie">
        <v-chart class="chart" :option="chartOption" autoresize />
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import VChart from 'vue-echarts'
import * as echarts from "echarts"
import { useProductsStore } from '@/stores/modules/products'
import { useDataStore } from '@/stores/modules/data'

const pdStore = useProductsStore()
const dataStore = useDataStore()
const props = defineProps({
    dev_ids: {
        type: Array,    
        default:[]        
    },
})
// 状态定义
const STATUS_MAP = {
    RUN: { name: '运行', color: '#67C23A' },
    STANDBY: { name: '待机', color: '#409EFF' },
    FAULT: { name: '故障', color: '#F56C6C' },
    OFFLINE: { name: '离线', color: '#909399' }
}

// 状态统计数据
const statusCount = ref({
    RUN: 0,
    STANDBY: 0,
    FAULT: 0,
    OFFLINE: 0
})

//根据给定的dev_ids去统计状态
const updateStatusCountByDevIds = () => {
    // 重置计数
    Object.keys(statusCount.value).forEach(key => {
        statusCount.value[key] = 0
    })
    
    // 统计设备状态
    props.dev_ids.forEach(dev_id => {
        const status = dataStore.devState[dev_id]?.status || "OFFLINE"
        if (statusCount.value.hasOwnProperty(status)) {
            statusCount.value[status]++
        } else {
            statusCount.value.OFFLINE++
        }
    })
}

// 更新状态统计
const updateStatusCount = (newStatus) => {
    // 重置计数
    Object.keys(statusCount.value).forEach(key => {
        statusCount.value[key] = 0
    })

    // 统计设备状态
    pdStore.devs.forEach(dev => {
        //如果dataStore中存在该设备的状态，则使用dataStore中的状态，否则使用dev中的状态
        const status = newStatus[dev.dev_id]?.status || "OFFLINE"
        if (statusCount.value.hasOwnProperty(status)) {
            statusCount.value[status]++
        } else {
            statusCount.value.OFFLINE++
        }
    })
}

// 图表配置
const chartOption = computed(() => ({
    title: {
        text: '',
        left: 'center'
    },
    tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
    },
    legend: {
        show: false
    },
    series: [
        {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: true,
            itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
            },
            label: {
                show: true,
                formatter: '{b}\n{c}台'
            },
            emphasis: {
                label: {
                    show: true,
                    fontSize: 12,
                    fontWeight: 'bold'
                }
            },
            data: Object.entries(statusCount.value).map(([key, value]) => ({
                name: STATUS_MAP[key].name,
                value: value,
                itemStyle: {
                    color: STATUS_MAP[key].color
                }
            }))
        }
    ]
}))

// 监听设备状态变化
watch(() => dataStore.devState, (newVal) => {
    updateStatusCountByDevIds()    
}, { deep: true })

// 初始化
onMounted(async () => {
    //await pdStore.getDevs()
    await dataStore.requestDevsState(pdStore.devs.map(device => device.dev_id));
    //dataStore.connectSocket()
    updateStatusCountByDevIds({})
})

</script>

<style scoped>
.dev-status-pie {
    width: 100%;
    height: 100%;
    min-height: 100px;
}

.chart {
    width: 100%;
    height: 100%;
    min-height: 100px;
}
</style>