# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 依赖目录
node_modules/

# 构建输出
dist/
dist-ssr/
build/
coverage/
*.tsbuildinfo

# 本地配置
*.local
.env
.env.*
!.env.example

# 测试输出
/cypress/videos/
/cypress/screenshots/
.coverage
.coverage.*
htmlcov/
.pytest_cache/
.cache/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# 编辑器配置
.vscode/
!.vscode/extensions.json
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Python/Django
*.pyc
*.pyo
*.pyd
__pycache__/
.Python
env*/
venv*/
ENV*/
env.bak/
venv.bak/
*.egg-info/
.eggs/
.tox/
.python-version

# Django 数据库和静态文件
db.sqlite3*
*.sqlite3
media/
static/
staticfiles/

# Jupyter Notebook
.ipynb_checkpoints/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 备份和临时文件
*.bak
*.backup
*.tmp
*~

# 可选：忽略 Django 迁移文件（如不需要同步迁移历史）
# */migrations/*.py
# !*/migrations/__init__.py

