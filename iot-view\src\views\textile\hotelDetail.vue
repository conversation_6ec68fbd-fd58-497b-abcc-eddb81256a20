<template>
    <div v-if="hotel" class="container">
        <!-- 返回按钮和标题 -->
        <el-page-header @back="goBack">
            <template #content>
                <div class="header-container">
                    <!-- 标题 -->
                    <div>{{ hotel.name }} </div>
                    <!-- 编辑文本 -->
                    <el-button  :icon="Edit" type="primary" @click="editHotel" text />
                </div>

            </template>
        </el-page-header>
        <el-divider style="margin: 20px 0px 10px 0px;" />

        <!-- 酒店基本信息 -->
        <el-descriptions :column="6" class="compact-descriptions">
            <el-descriptions-item label="ID">{{ hotel.hotel_id }}</el-descriptions-item>
            <el-descriptions-item label="联系人">{{ hotel.contact_name }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ hotel.contact_phone }}</el-descriptions-item>
            <el-descriptions-item label="地址">{{ hotel.address }}</el-descriptions-item>
            <el-descriptions-item label="状态">
                <!-- 状态tag -->
                <el-tag :type="hotel.status === 1 ? 'success' : 'danger'">{{ hotel.status === 1 ? '正常' : '停用'
                }}</el-tag>
            </el-descriptions-item>
        </el-descriptions>

        <!-- Tab 按钮 -->
        <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="device-tabs">
            <el-tab-pane label="布草信息" name="textile" />
            <el-tab-pane label="业务流水" name="business" />
            <el-tab-pane label="费用结算" name="expense" />
            <el-tab-pane label="洗涤工艺" name="texProcess" />
            <el-tab-pane label="设置" name="settings" />

        </el-tabs>
        <!-- 动态组件 -->
        <component :is="currentComponent" :hotel_id="hotel.hotel_id" />

        <!-- 编辑酒店,弹出dialog -->
        <el-dialog v-model="editHotelDialog" title="编辑酒店" width="50%">
            <HotelEdit :hotel_id="hotel.hotel_id" />
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Edit, Delete } from '@element-plus/icons-vue'
import { useTextileStore } from '@/stores/modules/textile';
import { updateHotelApi } from '@/api/textile';
import HotelEdit from './hotelEdit.vue'
import TexExpense from './texExpense.vue'
import HotelTexRecord from './hotelTexRecord.vue'
import TexProcess from './texProcess.vue'
// 导入各个 Tab 对应的组件

import Textile from './textiles.vue';

const route = useRoute();
const router = useRouter();
const texStore = useTextileStore();

const hotel_id = route.params.hotel_id;

if (!hotel_id || hotel_id === ':hotel_id') {
    console.log('hotel_id is invalid:', hotel_id);
    router.push('/hotels');
}

// 酒店信息
const hotel = ref({})
const status = ref(1)
const handleStatusChange = async () => {
    hotel.value.status = hotel.value.status === 1 ? 0 : 1
    console.log('hotel.value.status', hotel.value.status)
    await updateHotelApi({ id: hotel.value.id, status: hotel.value.status })
}

// 编辑酒店
const editHotelDialog = ref(false)
const editHotel = () => {
    editHotelDialog.value = true
}


onMounted(async () => {
    //确保组件加载时自动获取device，因为computered只有在依赖发生变化时才会重新计算，
    // 所以需要在组件加载时向后台请求device，再赋值给device
    hotel.value = await texStore.requestHotel(hotel_id)
    //确保获取到设备详情后，自动切换到连接Tab
    activeTab.value = 'textile'

});


const activeTab = ref(''); // 默认激活的 Tab

//根据 activeTab 动态加载组件
const currentComponent = computed(() => {
    switch (activeTab.value) {
        case 'textile':
            return Textile;        
        case 'settings':
            return Settings;
        case 'expense':
            return TexExpense;
        case 'business':
            return HotelTexRecord;
        case 'texProcess':  
            return TexProcess;
    }
});


// 返回上一页
const goBack = () => {
    //返回上一页面
    router.back();
};

// Tab 点击事件
const handleTabClick = (tab) => {
    activeTab.value = tab.props.name;
};


</script>

<style scoped>
.container {
    padding: 20px;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.device-detail {
    margin: 20px auto;
    padding: 20px;
    max-width: 1200px;

}

.device-tabs {
    margin-top: 20px;
}

.compact-descriptions {
    width: 70%;
    /* 调整宽度为父容器的50%，你可以根据需要调整这个值 */
    margin: 0 0;
    /* 水平居中 */
    text-align: left;
}

:deep .el-descriptions__body {
    background: #ffffff00 !important;
}
</style>