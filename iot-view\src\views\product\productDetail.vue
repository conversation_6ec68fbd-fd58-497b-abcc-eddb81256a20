<template>
    <div v-if="product" class="container">
        <el-page-header @back="goBack">
            <template #content>
                <span class="text-large font-600 mr-3">{{ product.name }} </span>
                <el-button :icon="Edit" type="primary" @click="editProduct" text />
            </template>
        </el-page-header>
        <el-divider />

        <!-- 设备基本信息 -->
        <el-descriptions :column="4" class="compact-descriptions">
            <el-descriptions-item label="ID">{{ product.pro_id }}</el-descriptions-item>
            <el-descriptions-item label="型号">{{ product.model }}</el-descriptions-item>
            <el-descriptions-item label="类别">{{ pdCategoryName }}</el-descriptions-item>
            <el-descriptions-item label="设备类型">{{ product.type }}</el-descriptions-item>
        </el-descriptions>

        <!-- Tab 按钮 -->
        <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="device-tabs">
            <el-tab-pane label="信息" name="info" />
            <el-tab-pane label="关联设备" name="assDevice" />
            <el-tab-pane label="属性" name="propDefinition" />
            <el-tab-pane label="事件" name="alerts" />
            <el-tab-pane label="规则" name="rules" />
            <el-tab-pane label="任务" name="tasks" />
            <el-tab-pane label="设置" name="settings" />
        </el-tabs>

        <!-- 动态组件 -->
        <component :is="currentComponent" :pro_id="product.pro_id" />

        <!-- 编辑产品 -->
        <el-dialog v-model="editProductDialog" title="编辑产品" width="50%" destroy-on-close>
            <product-edit :pro_id="product?.pro_id" @success="handleEditSuccess" />
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Edit } from '@element-plus/icons-vue';
import { getProductsApi } from '@/api/products';


// 导入各个 Tab 对应的组件
import AssDevice from './components/AssDevice.vue';
// import Info from './components/Info.vue';
import PropDefinition from '../prop/PropDefinition.vue';
import Rules from '../rule/ruleList.vue';
import Alarm from '../alarm/alarmList.vue';
import ProductEdit from './productEdit.vue';



const route = useRoute();
const router = useRouter();


const pro_id = ref(route.params.pro_id);
const product = ref({});

watch(() => route.params.pro_id, async (newId) => {
    if (!newId || newId === ':pro_id') {
        
        return;
    }
    pro_id.value = newId;
    product.value = null; // 清空当前产品数据
    let res = await getProductsApi(pro_id.value);
    product.value = res[0];
    console.log('product.value', product.value);
}, { immediate: true });

const pdCategoryName = computed(() => {
    const name = {
        "PRODUCTION": '生产类',
        "METER": '仪表类',
        "GATEWAY": '网关类',
        "OTHER": '其它类',
    }
    return name[product.value.category] || '未知类别';
});


const editProductDialog = ref(false);
const editProduct = () => {
    editProductDialog.value = true;
}
const handleEditSuccess = async () => {
    editProductDialog.value = false;
    let res = await getProductsApi(pro_id.value);
    product.value = res[0];
}



const activeTab = ref('assDevice'); // 默认激活的 Tab

//根据 activeTab 动态加载组件
const currentComponent = computed(() => {
    switch (activeTab.value) {
        case 'assDevice':
            return AssDevice;

        case 'propDefinition':
            return PropDefinition;
        case 'rules':
            return Rules;
        case 'alerts':
            return Alarm;
        // case 'tasks':
        //     return Tasks;
        // case 'settings':
        //     return Settings;
        // default:
        //     return Properties;
    }
});


// 返回上一页
const goBack = () => {
    router.back();
};

// Tab 点击事件
const handleTabClick = (tab) => {
    activeTab.value = tab.props.name;
};


</script>

<style scoped>
.container {
    padding: 20px;
}

.device-detail {
    margin: 20px auto;
    padding: 20px;
}

.device-tabs {
    margin-top: 10px;
}

.compact-descriptions {
    width: 60%;
    /* 调整宽度为父容器的50%，你可以根据需要调整这个值 */
    margin: 0 0;
    /* 水平居中 */
    text-align: left;
    margin-top: 10px;
}

:deep .el-descriptions__body {
    background: #ffffff00 !important;
}
</style>