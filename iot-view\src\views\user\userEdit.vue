<!-- 新建或编辑用户 -->
<template>
    <div class="rule-add">
        <!-- 表单内容 -->
        <el-form ref="formRef" :model="formData" :rules="rules" size="default" label-width="120px" class="rule-form" label-position="top">
            <!-- 账户 -->
            <el-form-item label="账户" prop="username">
                <el-input v-model="formData.username" placeholder="请输入账户" />
            </el-form-item>  
            <!-- 密码 -->
            <el-form-item v-if="!isEdit" label="密码" prop="password">
                <el-input v-model="formData.password" placeholder="请输入密码" type="password" />
            </el-form-item>
            <!-- 确认密码 -->
            <el-form-item v-if="!isEdit" label="确认密码" prop="confirm_password">
                <el-input placeholder="请输入确认密码" type="password" />
            </el-form-item>          
            <!-- 姓名 -->
            <el-form-item label="姓名" prop="ch_name">
                <el-input v-model="formData.ch_name" placeholder="请输入姓名" />
            </el-form-item>

            <!-- 洗涤价格,浮点数字格式 -->
            <el-form-item label="手机号" prop="phone">
                <el-input v-model="formData.phone" placeholder="请输入手机号" />
            </el-form-item>
            <!-- 部门 -->
            <el-form-item label="部门" prop="department">
                <el-input v-model="formData.department" placeholder="请输入部门" />
            </el-form-item>
            <!-- 角色 -->
            <el-form-item label="角色" prop="roles">
                <el-select v-model="formData.roles" placeholder="请选择角色" multiple>
                    <el-option v-for="role in user_roles" :key="role.id" :label="role.name" :value="role.name" />
                </el-select>
            </el-form-item>
        </el-form>
        <div class="footer">
            <el-button type="primary" @click="submitForm">{{ submitButtonText }}</el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch,defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { updateUserApi, getUserRolesApi } from '@/api/user';


const emit = defineEmits(['close'])
const props = defineProps({
    user: {
        type: Object,
    }    
})

// 用户角色
const user_roles = ref([])
//获取用户角色
const getUserRoles = async () => {
    const res = await getUserRolesApi()
    user_roles.value = res
    console.log('user_roles', user_roles.value)
}

// 表单数据
const formData = ref({
    username: '',    
    ch_name: '',
    password:"",    
    phone: '',
    department: '',
    roles: [],
})
//确认密码校验
const validatePassword2 = (rule, value, callback) => {
    if (value === '') {
        callback(new Error('请输入密码'));
    } else if (value !== changePasswordForm.value.password) {
        callback(new Error('密码不一致'));
    } else {
        callback();
    }
}
//密码校验
const validatePassword = (rule, value, callback) => {
    if (value === '') {
        callback(new Error('请输入确认密码'));
    } else if (value.length < 6) {
        callback(new Error('密码长度不能小于6位'));
    } else {
        callback();
    }
}
// 表单验证规则
const rules = {
    username: [{ required: true, message: '请输入账户', trigger: 'blur' }],    
    ch_name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],     
    roles: [{ required: true, message: '请选择角色', trigger: 'blur' }],    
    password: [{ trigger: 'blur', validator: validatePassword }],
    confirm_password: [{ trigger: 'blur', validator: validatePassword2 }]
}


const formRef = ref(null)

// 修改提交按钮文本
const submitButtonText = computed(() => isEdit.value ? '更新用户' : '新增用户')

// 页面标题计算属性
const pageTitle = computed(() => isEdit.value ? '编辑用户' : '新增用户')
// 是否编辑模式
const isEdit = ref(false)
watch(() => props.user, async (newVal) => {
    if (newVal) {
        isEdit.value = true        
        Object.assign(formData.value, newVal)

    }else{
        isEdit.value = false
        formData.value = {
            username: '',            
            ch_name: '',
            phone: '',
            department: '',
            password:"",    
            roles: [],
        }
    }
}, { immediate: true })


// 初始化加载
onMounted(async () => {    
    await getUserRoles()
})

// 提交表单
const submitForm = async () => {    
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
        if (valid) {
            try {                
                formData.value.groups=formData.value.roles
                delete formData.value.roles
                if (isEdit.value){
                    delete formData.value.password                    
                }
                await updateUserApi(formData.value)                
                emit('close')
            } catch (error) {
                ElMessage.error(isEdit.value ? '用户更新失败' : '用户创建失败')
            }
        }
    })
}


</script>

<style scoped>
.rule-add {
    padding: 20px;
    height: 100%;
    overflow-y: auto;    
}

.header {
    margin-bottom: 20px;
}

.rule-form {
    max-width: 600px;
}

.el-dialog__body {
    padding: 20px;
    background-color: #1e1e1e;
}

.condition-settings {
    margin: 20px;
}

.condition-row {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.el-form-item {
    .el-input {
        width: 300px;
        height: 32px;
    }

    .el-select {
        width: 300px;
        height: 32px;
    }
}
</style>
