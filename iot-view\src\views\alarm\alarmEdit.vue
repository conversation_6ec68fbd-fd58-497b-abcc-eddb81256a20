<template>
    <div class="rule-add">
        <!-- 标题栏 -->
        <div class="header">
            <el-page-header @back="goBack" title="返回" :content="pageTitle" />
        </div>

        <!-- 表单内容 -->
         <div class="tip">
            此处可以自定义设备的事件（报警）信息，通过指定属性和条件来产生事件。
            设备默认报警字段是event/alarm/fault，当字段有数据时，就会触发报警事件。
            也可以定义事件节点，当节点属性为1时触发。
         </div>
        <el-form ref="formRef" :model="formData" :rules="rules" size="default" label-width="120px" class="rule-form">
            <!-- 规则名称 -->
            <el-form-item label="事件名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入事件名称" />
            </el-form-item>

            <!-- 规则类型 -->
            <el-form-item label="事件类型" prop="type">
                <el-select v-model="formData.type" placeholder="请选择事件类型">
                    <el-option label="事件-EVENT" value="EVENT" />
                    <el-option label="状态-STATUS" value="STATUS" />
                    <el-option label="告警-ALARM" value="ALARM" />
                </el-select>
            </el-form-item>

            <!-- 报警级别 -->
            <el-form-item label="事件级别" prop="level">
                <el-select v-model="formData.level" placeholder="请选择事件级别">
                    <el-option label="调试" value="DEBUG" />
                    <el-option label="提示" value="INFO" />
                    <el-option label="告警" value="WARN" />
                    <el-option label="错误" value="ERROR" />
                    <el-option label="严重" value="FATAL" />
                </el-select>
            </el-form-item>

            <!-- 报警对象类型 -->
            <el-form-item label="事件对象类型" prop="target_type">
                <el-radio-group v-model="formData.target_type" @change="handletypeChange">
                    <el-radio label="PRODUCT">产品</el-radio>
                    <el-radio label="DEVICE">设备</el-radio>
                </el-radio-group>
            </el-form-item>

            <!-- 产品/设备选择 -->
            <el-form-item label="选择对象" prop="target_id">
                <!-- 当type为device时，多选；否则单选 -->
                <el-select v-model="formData.target_id" :multiple="true" :multiple-limit="objectLimit"
                    placeholder="请选择">
                    <el-option v-for="item in objectOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>

            <!-- 事件条件 -->
            <el-form-item label="事件条件" prop="properties">
                <div class="condition-settings">
                    <div v-for="(condition, index) in conditions" :key="index" class="condition-row">
                        <!-- Logical Operator -->
                        <el-select v-model="condition.logical_operator" placeholder="AND" style="width: 100px">
                            <el-option label="AND" value="AND" />
                            <el-option label="OR" value="OR" />
                        </el-select>

                        <!-- Attribute -->
                        <el-select v-model="condition.attribute" placeholder="请选择属性" style="width: 150px">
                            <el-option v-for="attr in propertyOptions" :key="attr.identifier"
                                :label="attr.identifier + '-' + attr.name" :value="attr.identifier" />
                        </el-select>

                        <!-- Operator -->
                        <el-select v-model="condition.operator" placeholder="请选择操作符" style="width: 120px">
                            <el-option label="大于" value=">" />
                            <el-option label="大于等于" value=">=" />
                            <el-option label="小于" value="<" />
                            <el-option label="小于等于" value="<=" />
                            <el-option label="等于" value="=" />
                            <el-option label="不等于" value="!=" />
                            <el-option label="范围" value="range" />
                            <el-option label="非范围" value="not_range" />
                        </el-select>

                        <!-- Threshold Min -->
                        <el-input v-model="condition.threshold_min" placeholder="最小值" style="width: 100px"
                            type="number" />

                        <!-- Threshold Max (only visible for range and not_range operators) -->
                        <el-input v-if="condition.operator === 'range' || condition.operator === 'not_range'"
                            v-model="condition.threshold_max" placeholder="最大值" style="width: 100px" type="number" />

                        <!-- Delete Button -->
                        <el-button @click="removeCondition(index)" type="danger" :icon="Delete" circle />
                    </div>
                    <!-- Add Condition Button -->
                    <el-button @click="addCondition" type="primary">添加条件</el-button>
                </div>
            </el-form-item>

            <!--触发后设备的状态 -->
            <el-form-item label="触发后设备状态" prop="device_status">
                <el-select v-model="formData.device_status" placeholder="请选择设备状态">
                    <el-option label="不变-KEEP" value="KEEP" />
                    <el-option label="运行-RUN" value="RUN" />
                    <el-option label="手动-MANUAL" value="MANUAL" />
                    <el-option label="待机-STANDBY" value="STANDBY" />
                    <el-option label="故障-FAULT" value="FAULT" />
                </el-select>
            </el-form-item>
            <!-- 触发后处理方式 -->
            <el-form-item label="触发后处理方式" prop="handle_type">
                <el-select v-model="formData.handle_type" placeholder="请选择处理方式">                    
                    <el-option label="仅推送" value="PUSH" />
                    <el-option label="仅保存" value="RECORD" />
                    <el-option label="推送并保存" value="PUSH_RECORD" />
                </el-select>
            </el-form-item>

            <!-- 复位方式,手动或自动 -->
            <el-form-item label="复位方式" prop="reset">
                <el-radio-group v-model="formData.reset" default-value="AUTO">
                    <el-radio label="MANUAL" value="MANUAL">手动</el-radio>
                    <el-radio label="AUTO" value="AUTO">自动</el-radio>
                </el-radio-group>
            </el-form-item>
            <!-- 事件记录方式 -->
            <el-form-item label="事件记录方式" prop="record_type">
                <el-select v-model="formData.record_type" placeholder="请选择事件记录方式">
                    <el-option label="不记录" value="None" />
                    <el-option label="记录自己的值" value="SELF" />
                    <el-option label="记录指定值" value="SPEC" />
                </el-select>
            </el-form-item>

            <!-- 选择需要记录的属性，可多选 -->
            <el-form-item v-if="formData.record_type === 'SPEC'" label="选择需要记录的属性" prop="record_prop">
                <el-select v-model="recordProps" :multiple="true" placeholder="请选择属性">
                    <el-option v-for="item in propertyOptions" :key="item.identifier"
                        :label="item.identifier + '-' + item.name" :value="item.identifier" />
                </el-select>

            </el-form-item>

            <!-- 规则描述 -->
            <el-form-item label="规则描述" prop="description">
                <el-input v-model="formData.description" placeholder="请输入规则描述" />
            </el-form-item>

            <!-- 提交按钮 -->
            <el-form-item>
                <el-button type="primary" @click="submitForm">{{ submitButtonText }}</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useProductsStore } from '@/stores/modules/products'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { getAlarmDetailApi, updateAlarmApi, getProductPropsApi } from '@/api/products'
const router = useRouter()
const route = useRoute()
const productsStore = useProductsStore()

// 表单数据
const formData = reactive({
    name: '',
    type: 'EVENT',
    level: 'INFO',
    target_type: 'PRODUCT',
    target_id: [],
    conditions: [],
    device_status: 'KEEP',
    handle_type: 'PUSH',
    record_type: 'None',
    record_prop: [],
    reset: 'AUTO',
    description: ''
})

// 表单验证规则
const rules = {
    name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择规则类型', trigger: 'change' }],
    target_id: [{ required: true, message: '请选择对象', trigger: 'change' }],
    description: [{ required: false, message: '请输入规则描述', trigger: 'blur' }]
}


const formRef = ref(null)
const objectLimit = ref(0)  //选择产品或设备的最大数量

// 计算属性：对象选项列表
const objectOptions = computed(() => {
    if (formData.target_type === 'PRODUCT') {
        objectLimit.value = 1

        return productsStore.products.map(item => ({
            id: item.pro_id,
            name: item.name
        }))

    } else {
        objectLimit.value = 0
        return productsStore.devs.map(item => ({
            id: item.dev_id,
            name: item.name
        }))
    }
})

// 属性选项列表
const propertyOptions = ref([])

//当选择的产品或设备改变时，重新获取属性
watch(() => formData.target_id, async (new_ids) => {
    console.log('formData.target_id改变', new_ids)
    if (new_ids.length === 0) return

    //判断是否数组格式
    if (!Array.isArray(new_ids)) {
        formData.target_id = [new_ids]
    }

    let pro_id = formData.target_id[0]
    console.log('formData.target_type', formData.target_type)
    if (formData.target_type === 'DEVICE') {
        const dev = productsStore.devs.find(item => item.dev_id === formData.target_id[0])
        pro_id = dev.pro_id

    }
    propertyOptions.value = await getProductPropsApi(pro_id)
})

// 判断是否为编辑模式 (修改路由参数判断)
const isEdit = computed(() => {
    const path = route.path
    return path.startsWith('/alarm/')
})
// 获取规则ID (修改获取ID的方式)
const getRuleId = computed(() => {
    if (!isEdit.value) return null
    return route.path.split('/').pop()
})

const rule_id = ref(route.params.rule_id);
console.log('rule_id', rule_id.value)


// 修改提交按钮文本
const submitButtonText = computed(() => isEdit.value ? '更新报警' : '创建报警')

// 页面标题计算属性
const pageTitle = computed(() => isEdit.value ? '编辑报警' : '创建报警')


// 获取报警详情
const fetchRuleDetail = async (id) => {
    try {
        const ruleDetail = await getAlarmDetailApi(id)
        console.log('ruleDetail', ruleDetail)
        // 填充表单数据
        Object.assign(formData, ruleDetail[0])
        // 解析conditions
        formData.conditions = JSON.parse(ruleDetail[0].conditions || '[]')
        //填充conditions
        conditions.value = formData.conditions
        //解析record_prop
        recordProps.value = JSON.parse(ruleDetail[0].record_prop || '[]')
        formData.record_prop = recordProps.value
    } catch (error) {
        ElMessage.error('获取报警详情失败')
    }
}

// 初始化加载
onMounted(async () => {
    await Promise.all([
        productsStore.requestProducts(),
        productsStore.requestDevs()
    ])

    // 如果是编辑模式，获取规则详情
    if (isEdit.value) {
        await fetchRuleDetail(getRuleId.value)
    }
})

// 对象类型改变处理
const handletypeChange = () => {
    formData.target_id = []
}

// 对象选择改变处理
const handleObjectSelect = () => {
    //确保单选模式返回数组    
    if (formData.type === 'device') {
        formData.target_id = [formData.target_id]
    }
}

const conditions = ref([
    {
        logical_operator: "AND",
        attribute: "",
        operator: "",
        threshold_min: null,
        threshold_max: null
    }
]);

// 添加一个条件
const addCondition = () => {
    conditions.value.push({
        logical_operator: "AND",
        attribute: "",
        operator: "",
        threshold_min: null,
        threshold_max: null
    });
};

// 删除某个条件
const removeCondition = (index) => {
    conditions.value.splice(index, 1);
};

//监听record_type的变化
watch(() => formData.record_type, (newVal) => {
    if (newVal === 'SELF') {
        //把conditions中的attribute添加到recordProps中
        recordProps.value = []
        conditions.value.forEach(item => {
            recordProps.value.push(item.attribute)
        })
    }
})

// 条件转换为 JSON 格式
const jsonConditions = computed(() => {
    //formData.conditions = JSON.stringify(conditions.value, null, 2)
    console.log('条件改变', conditions.value)
    return JSON.stringify(conditions.value, null, 2)
});

const recordProps = ref([])
//选择的属性转换为 JSON 数组 格式
const jsonRecordProp = computed(() => {
    //formData.record_prop = JSON.stringify(recordProps.value, null, 2)
    console.log('记录的属性改变', recordProps.value)
    return JSON.stringify(recordProps.value, null, 2)
});

// 返回上一页
const goBack = () => {
    router.back()
}

// 提交表单
const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
        if (valid) {
            try {
                //const newForm = new FormData()
                formData.conditions = JSON.stringify(conditions.value, null, 2)
                formData.record_prop = JSON.stringify(recordProps.value, null, 2)

                // newForm.append('name', formData.name)
                // newForm.append('type', formData.type)
                // newForm.append('target_type', formData.target_type)
                // newForm.append('target_id', formData.target_id)
                // newForm.append('conditions', conditions_json)
                // newForm.append('record_type', formData.record_type)
                // newForm.append('record_prop', record_prop_json)
                // newForm.append('reset', formData.reset)                
                // newForm.append('description', formData.description)

                console.log('提交的报警表单', formData)
                if (isEdit.value) {
                    formData.id = getRuleId.value  // 使用新的ID获取方式
                    // TODO: 调用更新报警API
                    await updateAlarmApi(formData)
                    ElMessage.success('报警更新成功')
                } else {
                    // TODO: 调用创建报警API
                    await updateAlarmApi(formData)
                    ElMessage.success('报警创建成功')
                }
                router.back()
            } catch (error) {
                ElMessage.error(isEdit.value ? '报警更新失败' : '报警创建失败')
            }
        }
    })
}
</script>

<style scoped>

.rule-add {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
    
}

.header {
    margin-bottom: 20px;
}
.tip{
    color: #999;
    font-size: 12px;
    line-height: 1.5;
    margin-bottom: 20px;
}

.rule-form {
    max-width: 800px;
    
}

.el-dialog__body {
    padding: 20px;
    background-color: #1e1e1e;
}

.condition-settings {
    margin: 20px;
}

.condition-row {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.el-form-item {
    .el-input {
        width: 300px;
        height: 32px;
    }
    .el-select {
        width: 300px;
        height: 32px;
    }
}

</style>
